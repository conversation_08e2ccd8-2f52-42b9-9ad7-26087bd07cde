import { useRef, useEffect, useState } from 'react';
import { shallow } from 'zustand/shallow';
import { StoreApi, UseBoundStore } from 'zustand';

/**
 * Custom hook that uses shallow comparison to prevent unnecessary re-renders
 * when accessing Zustand store state
 */
export function useStoreShallow<T, U>(
  store: UseBoundStore<StoreApi<T>>,
  selector: (state: T) => U
): U {
  const stateRef = useRef<U>(selector(store.getState()));
  const [, forceUpdate] = useState({});

  useEffect(() => {
    const unsubscribe = store.subscribe((state) => {
      const newState = selector(state);
      if (!shallow(stateRef.current, newState)) {
        stateRef.current = newState;
        forceUpdate({});
      }
    });

    return unsubscribe;
  }, [store, selector]);

  return stateRef.current;
}
