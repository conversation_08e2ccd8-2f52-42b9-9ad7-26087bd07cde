const { build } = require('esbuild');

// Pre-bundle Three.js and its common dependencies
build({
  entryPoints: ['three'],
  bundle: true,
  format: 'esm',
  outfile: './build/js/prebundled-three.js',
  plugins: [{
    name: 'three-stdlib',
    setup(build) {
      build.onResolve({ filter: /three\/examples\/jsm\// }, args => {
        return { path: args.path.replace('three/examples/jsm/', 'three-stdlib/') };
      });
    },
  }],
}).catch(() => process.exit(1));

// Main app build
build({
  entryPoints: ['src/main.js'],
  bundle: true,
  format: 'esm',
  outfile: 'dist/bundle.js',
  external: ['three'], // Mark pre-bundled Three as external
}).catch(() => process.exit(1));