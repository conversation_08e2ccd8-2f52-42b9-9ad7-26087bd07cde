import React from "react";
import { InventoryItemStack } from "src/Interfaces";
import { InventoryItem } from "../Inventory/InventoryItem";
import { Items } from "src/enums/resources";

interface StorageItemContentProps {
    itemStack: InventoryItemStack | null;
    isDragging: boolean;
    isOver: boolean;
    attributes: any;
    listeners: any;
    innerRef: React.Ref<HTMLDivElement>;
}

export const StorageItemContent = React.memo((props: StorageItemContentProps) => {
    const {
        itemStack,
        isDragging,
        isOver,
        attributes,
        listeners,
        innerRef
    } = props;


    if (!itemStack) {
        return (
            <div
                ref={innerRef as React.RefObject<HTMLDivElement>}
                className={`inventory-item empty ${isOver ? 'drag-over' : ''}`}
            >
                <div className="inventory-item-inner">
                </div>
            </div>
        );
    }

    const itemDef = itemStack ? Items[itemStack.itemId] : null;

    return (
        <div
            ref={innerRef}
            // className={`storage-item-slot ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''} ${isOver ? 'drag-over' : ''}`}
            className={`inventory-item${isOver ? ' drag-over' : ''}`}
            {...attributes}
            {...listeners}
            // style={{
            //     opacity: isDragging ? 0.5 : 1,
            //     transform: isDragging ? 'scale(1.05)' : 'scale(1)',
            //     transition: 'transform 0.2s ease',
            // }}
        >
            <div className="inventory-item-inner">
                <InventoryItem itemStack={itemStack} itemDef={itemDef} />
            </div>
        </div>
    );
});
