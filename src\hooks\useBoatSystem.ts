import { useState, useCallback, useEffect } from 'react';
import { Boat, BoatState } from '../Interfaces';
import { getText } from '../i18n';
import { useRootStore } from '../stores/rootStore';
import { Boats } from '../enums/boat_enums';
import { showDialog } from '../utils/dialog';

// interface ExtendedInventoryItemStack {
//     itemId: string;
//     itemType: string;
//     durability?: number;
//     quantity: number;
// }

// interface ExtendedRootState {
//     itemStacks: ExtendedInventoryItemStack[];
//     player: {
//         speed: number;
//         waterResistance: number;
//         insulation: number;
//         thermalShield: number;
//     };
//     gameStatus: {
//         weather: string;
//     };
// }

// export const useBoatSystem = () => {

//     // Show boat selection dialog
//     const showBoatSelection = useCallback(() => {
//         const boats = getPlayerBoats();
//         if (!boats.length) {
//             showDialog(getText('boat_required'));
//             return;
//         }
//         setShowBoatDialog(true);
//     }, [getPlayerBoats]);

//     return {
//         state,
//         showBoatDialog,
//         setShowBoatDialog,
//         getPlayerBoats,
//         selectBoat,
//         exitBoat,
//         showBoatSelection
//     };
// }; 