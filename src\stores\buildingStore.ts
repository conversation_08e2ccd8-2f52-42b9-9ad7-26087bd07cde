import { Building } from "src/Interfaces";
import { create } from "zustand";

interface BuildingStore {
    selectedBuildingToPlace: Building | null;
    setSelectedBuildingToPlace: (building: Building) => void;
    hoverPosition: { x: number, y: number } | null;
    setHoverPosition: (pos: { x: number, y: number }) => void;
    isValidPlacement: boolean;
    setIsValidPlacement: (isValid: boolean) => void;
}

export const useBuildingStore = create<BuildingStore>((set, get) => ({
    selectedBuildingToPlace: null,
    setSelectedBuildingToPlace: (building) => set({ selectedBuildingToPlace: building }),
    hoverPosition: null,
    setHoverPosition: (pos) => set({ hoverPosition: pos }),
    isValidPlacement: false,
    setIsValidPlacement: (isValid) => set({ isValidPlacement: isValid }),
}));
