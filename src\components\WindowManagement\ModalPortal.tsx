import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';

interface ModalPortalProps {
  children: React.ReactNode;
  portalId?: string; // Optional unique ID for this portal
}

// Keep track of all active portals
const activePortals: Record<string, number> = {};

export const ModalPortal: React.FC<ModalPortalProps> = ({
  children,
  portalId = 'modal-root'
}) => {
  const [modalRoot, setModalRoot] = useState<HTMLElement | null>(null);
  const portalRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    console.log(`ModalPortal mounting: ${portalId}`);

    // Find or create the modal root element
    let modalRootElement = document.getElementById(portalId);

    if (!modalRootElement) {
      console.log(`Creating new portal element with id: ${portalId}`);
      modalRootElement = document.createElement('div');
      modalRootElement.id = portalId;
      document.body.appendChild(modalRootElement);
      activePortals[portalId] = 1;
    } else {
      // Increment reference count
      activePortals[portalId] = (activePortals[portalId] || 0) + 1;
      console.log(`Using existing portal: ${portalId}, count: ${activePortals[portalId]}`);
    }

    portalRef.current = modalRootElement;
    setModalRoot(modalRootElement);

    // Clean up on unmount
    return () => {
      console.log(`ModalPortal unmounting: ${portalId}`);

      if (portalRef.current) {
        // Decrement reference count
        activePortals[portalId] = activePortals[portalId] - 1;

        // Only remove if this is the last reference
        if (activePortals[portalId] <= 0) {
          console.log(`Removing portal element: ${portalId}`);
          if (document.body.contains(portalRef.current)) {
            document.body.removeChild(portalRef.current);
          }
          delete activePortals[portalId];
        }
      }
    };
  }, [portalId]);

  if (!modalRoot) return null;

  return createPortal(children, modalRoot);
};
