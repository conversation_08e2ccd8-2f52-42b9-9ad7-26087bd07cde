import { gameStatus, startProgressBar } from "src/gameinfo";
import { getText } from "src/i18n";
import { Building, PlacedBuilding, StorageBuilding } from "src/Interfaces";
import { Items } from "./resources";
import { openFireLightUpInterface, openCookingInterface } from "src/cooking";
import React from 'react';
import { createRoot } from 'react-dom/client';
import { StorageModal } from "src/components/StorageModal";



const INTERACTIONS = {
    sleep: {
        id: 'sleep',
        get name() { return getText('interaction_sleep'); },
        icon: '💤',
        action: function() {
            startProgressBar(getText('Sleeping'), 480, () => { // 8 hours in game
                gameStatus.energy = 100;
            });
        },
        duration: 480 // 8 hours in game
    }
}

const STORAGE_BUILDINGS: { [key: string]: StorageBuilding} = {
    SMALL_STORAGE_BOX: {
        id: 'SMALL_STORAGE_BOX',
        get name() { return getText('SMALL_STORAGE_BOX'); },
        get description() { return getText('SMALL_STORAGE_BOX_desc'); },
        icon: '📦',
        resources: [
            { itemDef: Items.Log, quantity: 1 },
            { itemDef: Items.Stone, quantity: 1 },
            { itemDef: Items.BoneGlue, quantity: 1 },
        ],
        interactions: [
            {
                id: 'store',
                get name() { return getText('interaction_store'); },
                icon: '🗃️',
                action: function(building) {
                    // Open storage interface
                    openStorageInterface(building);
                }
            }
        ],
        width: 1,
        height: 1,
        storageCapacity: 20
    },
    MEDIUM_STORAGE_BOX: {
        id: 'MEDIUM_STORAGE_BOX',
        get name() { return getText('MEDIUM_STORAGE_BOX'); },
        get description() { return getText('MEDIUM_STORAGE_BOX_desc'); },
        icon: '📦',
        resources: [
            { itemDef: Items.Log, quantity: 1 },
            { itemDef: Items.Stone, quantity: 1 },
            { itemDef: Items.BoneGlue, quantity: 1 },
        ],
        interactions: [
            {
                id: 'store',
                get name() { return getText('interaction_store'); },
                icon: '🗃️',
                action: function(building) {
                    // Open storage interface
                    openStorageInterface(building);
                }
            }
        ],
        width: 1,
        height: 1,
        storageCapacity: 40
    },
}

export const BUILDINGS: { [key: string]: Building} = {
    ...STORAGE_BUILDINGS,
    SMALL_HOUSE: {
        id: 'SMALL_HOUSE',
        get name() { return getText('building_small_house'); },
        get description() { return getText('building_small_house_desc'); },
        icon: '🏠',
        resources: [
            { itemDef: Items.Log, quantity: 10 },
            { itemDef: Items.Stone, quantity: 5 },
        ],
        interactions: [INTERACTIONS.sleep],
        width: 1,
        height: 1
    },
    MEDIUM_HOUSE: {
        id: 'MEDIUM_HOUSE',
        get name() { return getText('building_small_house'); },
        get description() { return getText('building_small_house_desc'); },
        icon: '🏠',
        resources: [
            { itemDef: Items.Log, quantity: 10 },
            { itemDef: Items.Stone, quantity: 5 },
        ],
        interactions: [INTERACTIONS.sleep],
        width: 2,
        height: 2
    },
    LARGE_HOUSE: {
        id: 'LARGE_HOUSE',
        get name() { return getText('building_small_house'); },
        get description() { return getText('building_small_house_desc'); },
        icon: '🏠',
        resources: [
            { itemDef: Items.Log, quantity: 10 },
            { itemDef: Items.Stone, quantity: 5 },
        ],
        interactions: [INTERACTIONS.sleep],
        width: 3,
        height: 3
    },
    FIRE_PIT: {
        id: 'FIRE_PIT',
        get name() { return getText('building_fire_pit'); },
        get description() { return getText('building_fire_pit_desc'); },
        icon: '🔥',
        resources: [
            { itemDef: Items.Log, quantity: 10 },
            { itemDef: Items.Stone, quantity: 5 },
        ],
        interactions: [
            {
                id: 'light_fire',
                get name() { return getText('interaction_light_fire'); },
                icon: '🔥',
                action: function(building) {
                    // Check if fire is already lit
                    if (building.isLit) {
                        return;
                    }
                    // Open fire light up interface
                    openFireLightUpInterface(building);
                },
                condition: function(building) {
                    return !building.isLit;
                }
            },
            {
                id: 'cook',
                get name() { return getText('interaction_cook'); },
                icon: '🍳',
                action: function(building) {
                    // Open cooking interface
                    openCookingInterface(building);
                },
                condition: function(building) {
                    return building.isLit;
                }
            }
        ],
        width: 1,
        height: 1
    },
    GRASS_PLANT: {
        id: 'GRASS_PLANT',
        get name() { return getText('building_grass_plant'); },
        get description() { return getText('building_grass_plant_desc'); },
        icon: 'images/snowtree1.png',
        resources: [
            { itemDef: Items.Grass, quantity: 10 },
            { itemDef: Items.Water, quantity: 5 },
            { itemDef: Items.Manure, quantity: 2 },
        ],
        interactions: [],
        width: 1,
        height: 2,
    },
    TRADING_POST: {
        id: 'TRADING_POST',
        get name() { return getText('building_trading_post'); },
        get description() { return getText('building_trading_post_desc'); },
        icon: '🌲',
        resources: [
            { itemDef: Items.Grass, quantity: 1 },
            { itemDef: Items.Water, quantity: 1 },
        ],
        interactions: [],
        width: 1,
        height: 1,
    },
    pinetree: {
        id: 'pinetree',
        get name() { return getText('pinetree'); },
        get description() { return getText('pinetree'); },
        icon: 'images/trees/pinetree.png',
        resources: [
            { itemDef: Items.Grass, quantity: 0 }
        ],
        interactions: [],
        width: 1,
        height: 1,
    },
    TREE: {
        id: 'TREE',
        get name() { return getText('TREE'); },
        get description() { return getText('TREE'); },
        icon: 'images/trees/tree1.png,images/trees/tree2.png,images/trees/tree3.png',
        resources: [
            { itemDef: Items.Grass, quantity: 0 }
        ],
        interactions: [
            {
                id: 'chop',
                get name() { return getText('chop_tree'); },
                icon: '🍳',
                action: function() {
                }
            }
        ],
        width: 1,
        height: 1,
        multiIcons: true,
        hideInCraftList: true,
    },
    coconut_tree: {
        id: 'coconut_tree',
        get name() { return getText('coconut_tree'); },
        get description() { return getText('coconut_tree'); },
        icon: 'images/trees/CoconutTree1.png,images/trees/CoconutTree2.png,images/trees/CoconutTree3.png,images/trees/CoconutTree4.png',
        resources: [
            { itemDef: Items.Grass, quantity: 0 }
        ],
        interactions: [
            {
                id: 'chop',
                get name() { return getText('chop_tree'); },
                icon: '🍳',
                action: function() {
                }
            }
        ],
        width: 1,
        height: 1,
        multiIcons: true,
        hideInCraftList: true,
    },
    STONE: {
        id: 'STONE',
        get name() { return getText('STONE'); },
        get description() { return getText('STONE'); },
        icon: 'images/rocks/rock1.png,images/rocks/rock2.png,images/rocks/rock3.png,images/rocks/rock4.png',
        resources: [
            { itemDef: Items.Grass, quantity: 0 }
        ],
        interactions: [
            {
                id: 'clean_stone',
                get name() { return getText('clean_stone'); },
                icon: '🍳',
                action: function() {
                }
            }],
        width: 1,
        height: 1,
        multiIcons: true,
        hideInCraftList: true,
    },
    // TREE2: {
    //     id: 'TREE2',
    //     get name() { return getText('TREE2'); },
    //     get description() { return getText('TREE2'); },
    //     icon: 'images/trees/tree2.png',
    //     resources: [
    //         { itemDef: Items.Grass, quantity: 0 }
    //     ],
    //     interactions: [],
    //     width: 1,
    //     height: 1,
    // },
    // TREE3: {
    //     id: 'TREE3',
    //     get name() { return getText('TREE3'); },
    //     get description() { return getText('TREE3'); },
    //     icon: 'images/trees/tree3.png',
    //     resources: [
    //         { itemDef: Items.Grass, quantity: 0 }
    //     ],
    //     interactions: [],
    //     width: 1,
    //     height: 1,
    // },
    GARDEN_PLOT: {
        id: 'GARDEN_PLOT',
        get name() { return getText('building_garden_plot'); },
        get description() { return getText('building_garden_plot_desc'); },
        icon: '🌱',
        resources: [
            { itemDef: Items.Grass, quantity: 10 },
            { itemDef: Items.Water, quantity: 5 },
            { itemDef: Items.Log, quantity: 2 },
        ],
        interactions: [
            {
                id: 'plant',
                get name() { return getText('interaction_plant'); },
                icon: '🌿',
                action: function() {
                    // Open planting interface
                    openPlantingInterface();
                }
            },
            {
                id: 'harvest',
                get name() { return getText('interaction_harvest'); },
                icon: '🌾',
                action: function() {
                    startProgressBar(getText('Harvesting'), 30, () => {
                        // Add harvested items to inventory
                        // window.add.addItem('Vegetable', 3);
                    });
                },
                duration: 30 // 30 minutes in game
            }
        ],
        width: 1,
        height: 1
    }
};




// Storage system to manage modal instances
const storageSystem = {
    currentBuilding: null as PlacedBuilding | null,
    storageModalInstance: null as any
};

// Helper function to ensure modal container exists
function ensureModalContainer(containerId: string): HTMLElement {
    let container = document.getElementById(containerId);
    if (!container) {
        container = document.createElement('div');
        container.id = containerId;
        document.body.appendChild(container);
    }
    return container;
}

// Functions to be implemented for interactions
function openStorageInterface(building: PlacedBuilding) {
    console.log('Opening storage interface for', building);

    // Close any existing storage modal
    if (storageSystem.storageModalInstance) {
        storageSystem.storageModalInstance.unmount();
        storageSystem.storageModalInstance = null;
    }

    storageSystem.currentBuilding = building;

    // Create a unique container for this modal
    const containerId = `storage-modal-${building.id}`;
    const container = ensureModalContainer(containerId);

    // Create a new root and render the modal
    const modalRoot = createRoot(container);
    storageSystem.storageModalInstance = modalRoot;

    modalRoot.render(
        React.createElement(StorageModal, {
            building,
            isOpen: true,
            portalId: `storage-portal-${building.id}`,
            onClose: () => {
                console.log('Closing storage modal');
                // Update the building reference before closing the modal
                storageSystem.currentBuilding = null;

                // Unmount the modal completely after a short delay
                setTimeout(() => {
                    if (storageSystem.storageModalInstance) {
                        storageSystem.storageModalInstance.unmount();
                        storageSystem.storageModalInstance = null;

                        // Remove the container
                        if (container.parentNode) {
                            container.parentNode.removeChild(container);
                        }
                    }
                }, 100);
            }
        })
    );
}

function openPlantingInterface() {
    console.log('Opening planting interface');
    // Implementation would go here
}