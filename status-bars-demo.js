// Demo script to show how to update the status bars
// You can include this in your main JavaScript or call these functions from your game logic

// Function to update a specific status bar
function updateStatusBar(statName, percentage) {
    const bar = document.getElementById(`${statName}-bar`);
    if (bar) {
        // Clamp percentage between 0 and 100
        const clampedPercentage = Math.max(0, Math.min(100, percentage));
        bar.style.width = `${clampedPercentage}%`;
    }
}

// Function to update all status bars at once
function updateAllStatusBars(stats) {
    updateStatusBar('health', stats.health || 100);
    updateStatusBar('food', stats.food || 100);
    updateStatusBar('water', stats.water || 100);
    updateStatusBar('energy', stats.energy || 100);
}

// Demo function to simulate changing stats
function demoStatusBars() {
    let health = 100;
    let food = 100;
    let water = 100;
    let energy = 100;

    setInterval(() => {
        // Simulate gradual decrease
        health = Math.max(0, health - Math.random() * 2);
        food = Math.max(0, food - Math.random() * 1.5);
        water = Math.max(0, water - Math.random() * 1.8);
        energy = Math.max(0, energy - Math.random() * 2.2);

        // Update the bars
        updateAllStatusBars({
            health: health,
            food: food,
            water: water,
            energy: energy
        });

        // Reset when all bars are low
        if (health < 10 && food < 10 && water < 10 && energy < 10) {
            health = food = water = energy = 100;
        }
    }, 100);
}

// Initialize status bars when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set initial values to 100%
    updateAllStatusBars({
        health: 100,
        food: 100,
        water: 100,
        energy: 100
    });

    // Uncomment the line below to run the demo
    // demoStatusBars();
});

// Example usage in your game:
// updateStatusBar('health', 75);  // Set health to 75%
// updateStatusBar('food', 50);    // Set food to 50%
// updateAllStatusBars({ health: 80, food: 60, water: 90, energy: 40 });
