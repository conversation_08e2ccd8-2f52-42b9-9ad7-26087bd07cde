import { create } from 'zustand';
import { InventoryItemStack } from 'src/Interfaces';
import { useRootStore } from './rootStore';
import { v4 as uuidv4 } from 'uuid';
import { CRAFTABLE_EQUIPABLES } from 'src/enums/CRAFTABLE_enums';
import { DEFAULT_INV_SLOT } from 'src/settings';

// Define the equipment slots based on the existing types
export type EquipmentSlotType = 'HEAD' | 'BODY' | 'BACKPACK' | 'HAND' | 'FEET';

interface EquipmentState {
  // Equipment slots with their equipped items
  equippedItems: Record<EquipmentSlotType, InventoryItemStack | null>;
  
  // Actions
  equipItem: (itemStack: InventoryItemStack, slotType: EquipmentSlotType) => boolean;
  unequipItem: (slotType: EquipmentSlotType) => boolean;
  canEquipToSlot: (itemStack: InventoryItemStack, slotType: EquipmentSlotType) => boolean;
}

export const useEquipmentStore = create<EquipmentState>((set, get) => ({
  // Initial state - all slots empty
  equippedItems: {
    HEAD: null,
    BODY: null,
    BACKPACK: null,
    HAND: null,
    FEET: null
  },

  // Check if an item can be equipped to a specific slot
  canEquipToSlot: (itemStack: InventoryItemStack, slotType: EquipmentSlotType) => {
    // Get the item definition
    const itemDef = CRAFTABLE_EQUIPABLES[itemStack.itemId];
    
    // Check if the item is equipable
    if (!itemDef || itemDef.type.id !== 'Equipable') {
      return false;
    }
    
    // Check if the item has a slotType property
    if (!('slotType' in itemDef)) {
      return false;
    }
    
    // Check if the item's slot type matches the target slot
    return itemDef.slotType?.id === slotType;
  },

  // Equip an item to a slot
  equipItem: (itemStack: InventoryItemStack, slotType: EquipmentSlotType) => {
    // Check if the item can be equipped to this slot
    if (!get().canEquipToSlot(itemStack, slotType)) {
      return false;
    }
    
    // Get the current equipped item in this slot (if any)
    const currentEquipped = get().equippedItems[slotType];
    
    // Remove the item from inventory
    const rootStore = useRootStore.getState();
    const newItemStacks = [...rootStore.itemStacks];
    const itemIndex = newItemStacks.findIndex(stack => stack && stack.uuid === itemStack.uuid);
    
    if (itemIndex === -1) {
      return false;
    }
    
    // If there's more than one item in the stack, reduce the quantity
    if (itemStack.quantity > 1) {
      newItemStacks[itemIndex] = {
        ...itemStack,
        quantity: itemStack.quantity - 1
      };
    } else {
      // Otherwise remove the item from inventory
      newItemStacks[itemIndex] = null;
    }
    
    // Update the inventory
    rootStore.setItemStacks(newItemStacks);
    
    // If there's already an item equipped, add it back to inventory
    if (currentEquipped) {
      rootStore.addItemToInventory(CRAFTABLE_EQUIPABLES[currentEquipped.itemId]);
    }
    
    // Equip the new item
    set(state => ({
      equippedItems: {
        ...state.equippedItems,
        [slotType]: {
          ...itemStack,
          quantity: 1,
          // uuid: uuidv4() // Generate a new UUID for the equipped item
        }
      }
    }));
    
    return true;
  },

  // Unequip an item from a slot
  unequipItem: (slotType: EquipmentSlotType) => {
    const currentEquipped = get().equippedItems[slotType];
    const rootStore = useRootStore.getState();
    
    if (!currentEquipped) {
      console.error("Nothing equipped in slot", slotType);
      return false;
    } else if (slotType === 'BACKPACK') {
      // handle inventory slot reduced situation
      const emptySlotsIndex = rootStore.itemStacks.reduce((totalEmptySlots, stack, index) => {
          if (stack == null && index < DEFAULT_INV_SLOT) {
          totalEmptySlots.push(index);
          }
          return totalEmptySlots;
        }, []
      );

      const itemStacksInBackpack_Map = new Map();
      rootStore.itemStacks.forEach((stack, slotIndex) => {
        if (slotIndex >= DEFAULT_INV_SLOT && stack != null) {
          itemStacksInBackpack_Map.set(slotIndex, stack);
        }
      });
      // const itemStacksInBackpack = rootStore.itemStacks.filter((stack, index) => index >= DEFAULT_INV_SLOT && stack != null);
      const itemCountInBackpack = itemStacksInBackpack_Map.size;
      if ((emptySlotsIndex.length-1) < itemCountInBackpack) {
        console.error("Inventory is full");
        return false;
      }

      const newItemStacks = [...rootStore.itemStacks];
      let index = 0;
      itemStacksInBackpack_Map.forEach((stack, slotIndex) => {
        newItemStacks[slotIndex] = null;
        newItemStacks[emptySlotsIndex[index]] = stack;
        index++;
      });
      rootStore.setItemStacks(newItemStacks);
    }

    
    // Add the item back to inventory
    rootStore.addItemToInventory(CRAFTABLE_EQUIPABLES[currentEquipped.itemId]);
    
    // Remove the item from the equipment slot
    set(state => ({
      equippedItems: {
        ...state.equippedItems,
        [slotType]: null
      }
    }));
    
    return true;
  }
}));
