import React from "react";
import { InventoryItemStack } from "src/Interfaces";
import { StorageDragDropWrapper } from "./StorageDragDropWrapper";
import { StorageItemContent } from "./StorageItemContent";

export const StorageItemSlot = React.memo((props: {
    itemStack: InventoryItemStack | null,
    stackIndex: number,
    isActive: boolean,
}) => {
    // Create the data object
    const draggableData = {
        index: props.stackIndex,
        itemStack: props.itemStack
    };

    // Use the StorageDragDropWrapper to isolate the DnD context changes
    return (
        <StorageDragDropWrapper
            id={`${props.stackIndex}`}
            data={draggableData}
            disabled={!props.itemStack}
        >
            {({ ref, isDragging, isOver, attributes, listeners }) => (
                <StorageItemContent
                    itemStack={props.itemStack}
                    isDragging={isDragging}
                    isOver={isOver}
                    attributes={attributes}
                    listeners={listeners}
                    innerRef={ref}
                />
            )}
        </StorageDragDropWrapper>
    );
});
