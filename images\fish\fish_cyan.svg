<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="a2b81ca1-5107-43aa-82cf-9f53cf75c1b8"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="93fc97d9-50cb-461a-931a-f05c037753b9"  >
</g>
<g transform="matrix(1 0 0 1 540 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1.56 0 0 1.56 -156.42 129.48)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,205,205); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="14.66,-38.81 -43.79,21.44 -27,38.81 43.79,-34.17 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -190.95 112.31)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(120,188,188); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="7.92,-32.43 -36.75,16.85 -21.7,32.43 36.75,-27.83 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -22.17 139.84)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(120,188,188); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="44.97,-26.91 16.14,-31.51 -44.97,31.51 -11.19,30.99 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -71.38 136.62)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,205,205); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="18.5,-34.09 -47.63,34.09 -13.48,33.57 47.63,-29.45 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -120.54 133.39)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(120,188,188); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="20.83,-36.67 -49.96,36.31 -49.61,36.67 -16.17,36.16 49.96,-32.03 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -180.9 -104.58)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(114,183,181); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-24.29,-4.53 3.81,26.38 24.29,22.1 -14.58,-26.38 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -154.01 -126)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,205,205); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-31.78,-12.67 7.08,35.81 31.78,30.63 -21.51,-35.81 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -84.02 -138.9)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,205,205); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-10.32,-33.14 -38.76,-33.14 14.41,33.14 38.76,28.05 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -124.56 -134.41)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(114,183,181); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-37.87,-36.01 -40.36,-30.43 12.94,36.01 40.36,30.27 -12.81,-36.01 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -43.14 -142.88)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(114,183,181); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="36.48,25.61 -11.29,-30.6 -36.48,-30.6 12.6,30.6 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -33.21 -21.24)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="128.31,-87.14 128.31,87.14 -55.45,87.14 -79.04,61.66 -80.92,59.66 -85.73,54.51 -109.33,29.05 -128.31,29.05 -128.31,-47.03 -109.33,-47.03 -95.16,-54.52 -81.45,-61.74 -81.05,-61.94 -40.61,-83.26 -33.25,-87.14 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -112.38 -137.54)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,118,117); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-184.08, -34.01)" d="M 214.861 21.309 C 214.861 29.794 210.714 37.283 204.297 41.832 C 200.088 44.908 194.945 46.709 189.393 46.709 L 153.292 46.709 L 153.701 46.505 L 194.14 25.183000000000003 L 201.493 21.309000000000005 L 214.861 21.309000000000005 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -145.07 -66.62)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-163.16, -79.4)" d="M 212.72 79.394 C 212.72 80.601 212.652 81.802 212.454 82.941 C 211.519 89.959 207.707 96.044 202.23000000000002 100.06 C 198.02100000000002 103.061 192.865 104.868 187.252 104.868 L 139.058 104.868 C 124.946 104.868 113.58999999999999 93.437 113.58999999999999 79.39399999999999 C 113.58999999999999 72.314 116.39299999999999 66.02499999999999 120.942 61.41499999999999 L 125.41699999999999 61.41499999999999 L 139.58999999999997 53.92699999999999 L 187.25199999999998 53.92699999999999 C 192.86499999999998 53.92699999999999 198.021 55.739999999999995 202.23 58.74199999999999 C 206.70399999999998 62.01599999999999 210.11499999999998 66.701 211.65599999999998 72.18499999999999 C 212.385 74.455 212.72 76.856 212.72 79.394 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -112.17 95.01)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,118,117); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-184.21, -182.84)" d="M 212.72 195.582 L 179.292 195.582 L 155.70600000000002 170.108 L 187.25100000000003 170.108 C 192.86400000000003 170.108 198.02000000000004 171.915 202.22900000000004 174.923 C 208.579 179.54 212.72 187.09 212.72 195.582 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -145.07 24.14)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-163.15, -137.49)" d="M 212.72 137.491 C 212.72 139.961 212.385 142.37400000000002 211.72299999999998 144.64600000000002 C 210.11399999999998 150.12400000000002 206.70399999999998 154.871 202.22899999999998 158.145 C 198.01999999999998 161.15300000000002 192.86399999999998 162.954 187.25099999999998 162.954 L 149.021 162.954 L 125.41599999999998 137.49200000000002 L 113.58899999999998 137.49200000000002 C 113.58899999999998 130.47400000000002 116.39199999999998 124.05500000000002 121.00999999999999 119.43900000000002 C 125.621 114.82800000000002 132.039 112.02500000000002 139.057 112.02500000000002 L 187.25099999999998 112.02500000000002 C 192.86399999999998 112.02500000000002 198.01999999999998 113.76400000000002 202.22899999999998 116.83300000000003 C 207.63799999999998 120.77600000000002 211.451 126.86000000000003 212.45299999999997 133.87800000000004 C 212.651 135.077 212.72 136.29 212.72 137.491 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -300.22 -154.1)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(120,188,188); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-3.05,-19.01 -27.37,19.01 27.37,19.01 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -300.22 83.55)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(120,188,188); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-3.05,19.01 27.37,-19.01 -27.37,-19.01 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -316.85 -35.28)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(120,188,188); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="53.21,-19.01 -41.05,-19.01 -53.21,0.01 -41.05,19.01 53.21,19.01 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -307.35 -94.69)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,205,205); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="47.13,0.01 31.93,-19.01 -22.8,-19.01 -47.13,19.01 47.13,19.01 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -307.35 24.14)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,205,205); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="47.13,-19.01 -47.13,-19.01 -22.8,19.01 31.93,19.01 47.13,0 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -82.4 -117.64)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-203.26, -46.75)" d="M 252.825 46.709 C 252.825 49.247 252.49699999999999 51.654 251.767 53.926 C 248.691 64.491 238.857 72.183 227.363 72.183 L 179.162 72.183 C 167.594 72.183 157.835 64.49000000000001 154.76500000000001 53.926 C 154.03500000000003 51.655 153.70100000000002 49.247 153.70100000000002 46.709 L 153.70100000000002 46.505 L 194.14000000000001 25.183000000000003 L 201.49300000000002 21.309000000000005 L 227.36300000000003 21.309000000000005 C 241.4 21.309 252.825 32.672 252.825 46.709 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -82.42 -21.27)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-203.25, -108.42)" d="M 252.825 108.427 C 252.825 122.482 241.42999999999998 133.883 227.369 133.883 L 179.131 133.883 C 165.07 133.883 153.675 122.48200000000001 153.675 108.427 L 153.675 108.427 C 153.675 94.36500000000001 165.07000000000002 82.965 179.131 82.965 L 227.369 82.965 C 241.431 82.965 252.825 94.365 252.825 108.427 L 252.825 108.427 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -82.3 75.12)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-203.33, -170.11)" d="M 155.706 170.107 L 179.29199999999997 195.581 L 191.664 195.581 L 212.72 195.581 L 227.363 195.581 C 241.4 195.581 252.825 184.14999999999998 252.825 170.107 C 252.825 167.637 252.49699999999999 165.23 251.82899999999998 162.952 C 248.68999999999997 152.399 238.93099999999998 144.645 227.36299999999997 144.645 L 211.72299999999996 144.645 L 192.73499999999996 144.645 L 179.16199999999995 144.645 C 172.14399999999995 144.645 165.73199999999994 147.517 161.11499999999995 152.13400000000001 C 158.10699999999994 155.13500000000002 155.89699999999996 158.818 154.76499999999996 162.95200000000003 C 154.22699999999995 164.62300000000002 153.89899999999994 166.29400000000004 153.83099999999996 168.10100000000003 L 155.706 170.107 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -19.47 -137.54)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-243.54, -34.01)" d="M 292.937 21.309 C 292.937 29.794 288.797 37.283 282.378 41.832 C 278.163 44.908 273.02 46.709 267.46799999999996 46.709 L 219.20599999999996 46.709 C 213.66099999999997 46.709 208.51099999999997 44.908 204.29699999999997 41.832 C 198.94899999999996 38.019 195.13599999999997 32.071 194.13999999999996 25.183 L 201.49299999999997 21.309 L 292.937 21.309 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -23.09 -66.66)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-241.22, -79.38)" d="M 290.802 79.375 C 290.802 93.431 279.401 104.837 265.34000000000003 104.837 L 217.10800000000003 104.837 C 203.04700000000003 104.837 191.64600000000004 93.43 191.64600000000004 79.375 L 191.64600000000004 79.375 C 191.64600000000004 65.313 203.04600000000005 53.913 217.10800000000003 53.913 L 265.34000000000003 53.913 C 279.401 53.913 290.802 65.313 290.802 79.375 L 290.802 79.375 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -23.07 95.01)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-241.23, -182.84)" d="M 290.802 195.582 L 191.66500000000002 195.582 C 191.66500000000002 188.49599999999998 194.47500000000002 182.13899999999998 199.08 177.535 C 200.089 176.594 201.092 175.66 202.23000000000002 174.923 C 206.43800000000002 171.915 211.58200000000002 170.108 217.133 170.108 L 265.32800000000003 170.108 C 270.942 170.108 276.098 171.915 280.30600000000004 174.923 C 286.655 179.54 290.802 187.09 290.802 195.582 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 -23.09 24.11)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-241.22, -137.47)" d="M 290.802 137.466 C 290.802 151.52200000000002 279.401 162.928 265.34000000000003 162.928 L 217.10800000000003 162.928 C 203.04700000000003 162.928 191.64600000000004 151.521 191.64600000000004 137.466 L 191.64600000000004 137.466 C 191.64600000000004 123.405 203.04600000000005 112.01 217.10800000000003 112.01 L 265.34000000000003 112.01 C 279.401 112.01 290.802 123.405 290.802 137.466 L 290.802 137.466 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 39.57 -117.65)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-281.32, -46.74)" d="M 330.9 46.74 C 330.9 60.795 319.5 72.202 305.438 72.202 L 257.2 72.202 C 243.13899999999998 72.202 231.744 60.795 231.744 46.739999999999995 L 231.744 46.739999999999995 C 231.744 32.678 243.138 21.283999999999995 257.2 21.283999999999995 L 305.438 21.283999999999995 C 319.5 21.284 330.9 32.678 330.9 46.74 L 330.9 46.74 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 39.57 -21.27)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-281.32, -108.42)" d="M 330.9 108.427 C 330.9 122.482 319.5 133.883 305.438 133.883 L 257.2 133.883 C 243.13899999999998 133.883 231.744 122.48200000000001 231.744 108.427 L 231.744 108.427 C 231.744 94.36500000000001 243.138 82.965 257.2 82.965 L 305.438 82.965 C 319.5 82.965 330.9 94.365 330.9 108.427 L 330.9 108.427 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 39.57 75.11)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-281.32, -170.11)" d="M 330.9 170.101 C 330.9 184.163 319.5 195.563 305.438 195.563 L 257.2 195.563 C 243.13899999999998 195.563 231.744 184.16299999999998 231.744 170.101 L 231.744 170.101 C 231.744 156.04 243.138 144.652 257.2 144.652 L 305.438 144.652 C 319.5 144.652 330.9 156.04 330.9 170.101 L 330.9 170.101 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 96 -137.54)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-317.44, -34.01)" d="M 363.059 21.309 L 363.059 39.691 C 358.516 44.035000000000004 352.37100000000004 46.709 345.54400000000004 46.709 L 297.28100000000006 46.709 C 291.73600000000005 46.709 286.5930000000001 44.908 282.37800000000004 41.832 C 275.96000000000004 37.283 271.819 29.794 271.819 21.309 L 363.059 21.309 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 98.9 -66.66)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-319.29, -79.38)" d="M 368.871 79.375 C 368.871 93.431 357.477 104.837 343.41499999999996 104.837 L 295.17699999999996 104.837 C 281.11499999999995 104.837 269.715 93.43 269.715 79.375 L 269.715 79.375 C 269.715 65.313 281.11499999999995 53.913 295.17699999999996 53.913 L 343.41499999999996 53.913 C 357.477 53.913 368.871 65.313 368.871 79.375 L 368.871 79.375 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 94.37 95.01)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-316.4, -182.84)" d="M 363.059 179.335 L 363.059 195.582 L 269.74 195.582 C 269.74 188.49599999999998 272.55 182.13899999999998 277.16700000000003 177.535 C 278.163 176.594 279.16600000000005 175.66 280.305 174.923 C 284.514 171.915 289.595 170.108 295.146 170.108 L 343.415 170.108 C 351.3 170.107 358.381 173.722 363.059 179.335 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 98.9 24.11)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-319.29, -137.47)" d="M 368.871 137.466 C 368.871 151.52200000000002 357.477 162.928 343.41499999999996 162.928 L 295.17699999999996 162.928 C 281.11499999999995 162.928 269.715 151.521 269.715 137.466 L 269.715 137.466 C 269.715 123.405 281.11499999999995 112.01 295.17699999999996 112.01 L 343.41499999999996 112.01 C 357.477 112.01 368.871 123.405 368.871 137.466 L 368.871 137.466 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 161.55 -117.65)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-359.39, -46.74)" d="M 408.97 46.74 C 408.97 60.795 397.569 72.202 383.50800000000004 72.202 L 335.27 72.202 C 321.20799999999997 72.202 309.81399999999996 60.795 309.81399999999996 46.739999999999995 L 309.81399999999996 46.739999999999995 C 309.81399999999996 32.678 321.20799999999997 21.283999999999995 335.27 21.283999999999995 L 383.508 21.283999999999995 C 397.569 21.284 408.97 32.678 408.97 46.74 L 408.97 46.74 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 161.55 -21.27)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-359.39, -108.42)" d="M 408.97 108.427 C 408.97 122.482 397.569 133.883 383.50800000000004 133.883 L 335.27 133.883 C 321.20799999999997 133.883 309.81399999999996 122.48200000000001 309.81399999999996 108.427 L 309.81399999999996 108.427 C 309.81399999999996 94.36500000000001 321.20799999999997 82.965 335.27 82.965 L 383.508 82.965 C 397.569 82.965 408.97 94.365 408.97 108.427 L 408.97 108.427 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 161.55 75.11)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(110,197,196); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-359.39, -170.11)" d="M 408.97 170.101 C 408.97 184.163 397.569 195.563 383.50800000000004 195.563 L 335.27 195.563 C 321.20799999999997 195.563 309.81399999999996 184.16299999999998 309.81399999999996 170.101 L 309.81399999999996 170.101 C 309.81399999999996 156.04 321.20799999999997 144.652 335.27 144.652 L 383.508 144.652 C 397.569 144.652 408.97 156.04 408.97 170.101 L 408.97 170.101 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 234.87 122.94)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(120,188,188); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-21.22,-30.74 -14.85,33.67 21.22,33.67 -8.48,-33.67 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 263.8 -21.24)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(84,150,150); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="87.17,-87.14 87.17,-36.47 -15.78,87.14 -61.77,87.14 -87.17,-5.79 -61.77,-87.14 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 261.4 -93.06)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-423.3, -62.48)" d="M 442.311 62.473 C 442.311 72.976 433.80699999999996 81.49199999999999 423.304 81.49199999999999 C 412.801 81.49199999999999 404.28499999999997 72.97599999999998 404.28499999999997 62.47299999999999 C 404.28499999999997 51.97599999999999 412.80199999999996 43.465999999999994 423.304 43.465999999999994 C 433.807 43.466 442.311 51.976 442.311 62.473 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 261.41 -93.06)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,81,81); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-423.3, -62.48)" d="M 432.618 62.473 C 432.618 67.622 428.453 71.787 423.303 71.787 C 418.154 71.787 413.989 67.622 413.989 62.473000000000006 C 413.989 57.330000000000005 418.154 53.16400000000001 423.303 53.16400000000001 C 428.453 53.164 432.618 57.33 432.618 62.473 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.56 0 0 1.56 163.57 132.08)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(134,205,205); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="29.27,-30.74 20.49,33.67 -29.27,33.67 11.71,-33.67 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 344.85 -78.74)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(70,133,130); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="35.3,-42.18 35.3,-22.59 -25.53,42.18 -35.3,33.02 " />
</g>
</g>
</g>
</svg>