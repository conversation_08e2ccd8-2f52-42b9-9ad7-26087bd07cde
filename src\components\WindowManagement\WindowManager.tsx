import React from 'react';
import { Mac<PERSON>Window } from './MacOSWindow';
import { useWindowStore } from '../../stores/windowStore';
import { CharacterPanel } from '../Windows/CharacterPanel';
import { InventoryPanel } from '../ReactInventory';
import { ReactCrafting } from '../ReactCrafting';
import { BuildingSystemRoot } from '../BuildingSystem';
import { EnvironmentDisplay } from '../ResourcesPanel';
import { EquipmentPanel } from '../Windows/EquipmentPanel';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';

export const WindowManager: React.FC = () => {
  const {
    windows,
    closeWindow,
    bringToFront,
    setWindowSize
  } = useWindowStore();
  const biomesList = useRootStore(state => state.biomesList);
  const currRegionIndex = useRootStore(state => state.currRegionIndex);

  // Render all possible windows, AnimatePresence in MacOSWindow will handle visibility
  return (
    <div className="window-manager">
      <MacOSWindow
        title={biomesList[currRegionIndex]}
        isOpen={windows.Explore.isOpen}
        onClose={() => closeWindow('Explore')}
        initialPosition={windows.Explore.position}
        initialSize={windows.Explore.size}
        zIndex={windows.Explore.zIndex}
        onFocus={() => bringToFront('Explore')}
        onResize={(size: { width: number, height: number }) => setWindowSize('Explore', size)}
      >
        <EnvironmentDisplay terrainName='xxx' />
      </MacOSWindow>

      <MacOSWindow
        title={getText("Inventory")}
        isOpen={windows.Inventory.isOpen}
        onClose={() => closeWindow('Inventory')}
        initialPosition={windows.Inventory.position}
        initialSize={windows.Inventory.size}
        zIndex={windows.Inventory.zIndex}
        onFocus={() => bringToFront('Inventory')}
        onResize={(size: { width: number, height: number }) => setWindowSize('Inventory', size)}
      >
        <InventoryPanel />
      </MacOSWindow>

      <MacOSWindow
        title={getText("Craft")}
        isOpen={windows.Craft.isOpen}
        onClose={() => closeWindow('Craft')}
        initialPosition={windows.Craft.position}
        initialSize={windows.Craft.size}
        zIndex={windows.Craft.zIndex}
        onFocus={() => bringToFront('Craft')}
        onResize={(size: { width: number, height: number }) => setWindowSize('Craft', size)}
      >
        <ReactCrafting />
      </MacOSWindow>

      <MacOSWindow
        title={getText("Buildings")}
        isOpen={windows.Ground.isOpen}
        onClose={() => closeWindow('Ground')}
        initialPosition={windows.Ground.position}
        initialSize={windows.Ground.size}
        zIndex={windows.Ground.zIndex}
        onFocus={() => bringToFront('Ground')}
        onResize={(size: { width: number, height: number }) => setWindowSize('Ground', size)}
      >
          <BuildingSystemRoot />
      </MacOSWindow>

      <MacOSWindow
        title={getText("Equipment")}
        isOpen={windows.Equipment.isOpen}
        onClose={() => closeWindow('Equipment')}
        initialPosition={windows.Equipment.position}
        initialSize={windows.Equipment.size}
        zIndex={windows.Equipment.zIndex}
        onFocus={() => bringToFront('Equipment')}
        onResize={(size: { width: number, height: number }) => setWindowSize('Equipment', size)}
      >
        <EquipmentPanel />
      </MacOSWindow>

      <MacOSWindow
        title={getText("Character")}
        isOpen={windows.Character.isOpen}
        onClose={() => closeWindow('Character')}
        initialPosition={windows.Character.position}
        initialSize={windows.Character.size}
        zIndex={windows.Character.zIndex}
        onFocus={() => bringToFront('Character')}
        onResize={(size: { width: number, height: number }) => setWindowSize('Character', size)}
      >
        <CharacterPanel />
      </MacOSWindow>
      
    </div>
  );
};
