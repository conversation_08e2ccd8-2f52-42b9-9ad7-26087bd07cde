import React, { useState, useRef, useEffect, useId } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ModalPortal } from './ModalPortal';

interface MacOSModalProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  initialSize?: { width: number, height: number };
  portalId?: string;
}

export const MacOSModal: React.FC<MacOSModalProps> = ({
  title,
  isOpen,
  onClose,
  children,
  initialSize = { width: 500, height: 400 },
  portalId,
}) => {
  const [size, setSize] = useState(initialSize);
  const modalRef = useRef<HTMLDivElement>(null);
  const uniqueId = useId();
  const modalPortalId = portalId || `modal-root-${uniqueId.replace(/:/g, '-')}`;

  console.log("MacOSModal rendered, title:", title, "isOpen:", isOpen, "portalId:", modalPortalId);

  useEffect(() => {
    console.log('MacOSModal mounted, title:', title, 'isOpen:', isOpen);
    return () => {
      console.log('MacOSModal unmounted, title:', title);
    };
  }, [title]);

  // Handle close
  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose();
  };

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  // Prevent scrolling and interaction with the body when modal is open
  useEffect(() => {
    if (isOpen) {
      // Save the current overflow style
      const originalStyle = window.getComputedStyle(document.body).overflow;
      // Prevent scrolling and add modal-open class
      document.body.style.overflow = 'hidden';
      document.body.classList.add('modal-open');

      // Re-enable scrolling and remove modal-open class when modal closes
      return () => {
        document.body.style.overflow = originalStyle;
        document.body.classList.remove('modal-open');
      };
    }
  }, [isOpen]);

  // Only render the portal when isOpen is true
  if (!isOpen) {
    return null;
  }

  return (
    <ModalPortal portalId={modalPortalId}>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="macos-modal-backdrop"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            onClick={handleBackdropClick}
          >
            <motion.div
              ref={modalRef}
              className={"macos-window macos-modal dark-mode " + title}
              style={{
                width: size.width,
                height: size.height,
              }}
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{
                scale: 1,
                opacity: 1
              }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: 'spring', damping: 20, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="window-titlebar" style={{ cursor: 'default' }}>
                <div className="window-controls">
                  <button className="window-control close" onClick={handleClose}></button>
                </div>
                <div className="window-title">{title}</div>
                <div className="window-titlebar-spacer"></div>
              </div>

              <motion.div
                className="window-content scrollable-container"
                animate={{
                  opacity: 1,
                  height: 'auto'
                }}
                transition={{ duration: 0.2 }}
              >
                {children}
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </ModalPortal>
  );
};
