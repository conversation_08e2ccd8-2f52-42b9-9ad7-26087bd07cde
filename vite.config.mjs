import { defineConfig } from 'vite';
import path from "path"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react"
// import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
    build: {
        minify: true,
        sourcemap: true,
        rollupOptions: {
            input: 'src/mapgen2.js', // Or 'mapgen2.ts' if TS entry
            output: {
                dir: 'build/js',
                entryFileNames: '_bundle.js',
            },
        },
        watch: {
            chokidar: {
                usePolling: true,
                interval: 3000,
            }
        }
    },
    //   plugins: [
    //     visualizer({ open: true }), // Optional: bundle analysis
    //   ],
    plugins: [react(), tailwindcss()],
    resolve: {
        alias: {
            "@": path.resolve(__dirname, "./src"),
        },
    },
    // Explicitly support both TS and JS
    esbuild: {
        include: /\.(js|ts)$/, // Process both file types
    },
    server: {
        watch: {
            usePolling: true,  // Required for Docker/WSL2
            interval: 1000,    // Poll every 1 second
        },
    },
});
