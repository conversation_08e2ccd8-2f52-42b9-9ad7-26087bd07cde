{
    "compilerOptions": {
        "target": "es6",
        "module": "commonjs",
        "lib": ["es6", "dom"],
        "jsx": "react",
        "sourceMap": true,
        "outDir": "./dist",
        "strict": true,
        "moduleResolution": "node",
        "baseUrl": "./",
        "esModuleInterop": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "checkJs": true, // Enable type checking for JS files
        "strictNullChecks": false, // Keep null checks enabled (recommended)
        "allowJs": true, // Allow JavaScript files to be imported
        "noImplicitAny": false,
        // "types": ["three", "@react-three/fiber", "@react-three/drei"]
    },
    "include": [
        "src/**/*",
        "**/*.js",
        "**/*.ts",
        "**/*.tsx",
        // "vite.config.mjs"
    ],
    "exclude": [
        "node_modules",
        "**/*.spec.ts"
    ]
}