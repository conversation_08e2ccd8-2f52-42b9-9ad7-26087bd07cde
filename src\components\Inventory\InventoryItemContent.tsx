import React from "react";
import { InventoryItemStack } from "src/Interfaces";
import { Items } from "src/enums/resources";
import { ItemIcon } from "../common";

// Memoize the InventoryItem component to prevent unnecessary re-renders
const InventoryItem = React.memo((props: {
    itemStack: InventoryItemStack,
    itemDef: any
}) => {
   return (
      <>
        <ItemIcon itemDef={props.itemDef} />
        {props.itemStack.quantity > 1 && <span className="item-quantity">{props.itemStack.quantity}</span>}
      </>
   )
});

// Props for the InventoryItemContent component
interface InventoryItemContentProps {
    itemStack: InventoryItemStack;
    isSelected: boolean;
    setSelectedStack: (itemStack: InventoryItemStack, element?: HTMLElement) => void;
    isDragging: boolean;
    isOver: boolean;
    attributes?: any;
    listeners?: any;
    innerRef?: React.Ref<HTMLDivElement>;
}

// Create a component that handles only the rendering of the item
export const InventoryItemContent = React.memo((props: InventoryItemContentProps) => {

    console.log("slot content rendered!!!!", props.isSelected);

    const {
        itemStack,
        isSelected,
        setSelectedStack,
        isDragging,
        isOver,
        attributes,
        listeners,
        innerRef
    } = props;

    if (!itemStack) {
        return (
            <div
                ref={innerRef as React.RefObject<HTMLDivElement>}
                className={`inventory-item empty ${isOver ? 'drag-over' : ''}`}
            >
                <div className="inventory-item-inner">
                </div>
            </div>
        );
    }

    const itemDef = Items[itemStack.itemId];

    if (!itemDef) {
        console.error("Item definition not found for itemStack", itemStack);
    }

    return (
        <>
            <div
                ref={innerRef as React.RefObject<HTMLDivElement>}
                className={`inventory-item${isOver ? ' drag-over' : ''}${isSelected ? ' selected' : ''}`}
            >
                <div
                    className="inventory-item-inner"
                    {...attributes}
                    {...listeners}
                    onClick={(e) => {
                        // Use the parent element (inventory-item) as the anchor for the popover
                        // This gives us the full item cell as the reference point
                        const itemElement = e.currentTarget.closest('.inventory-item');
                        console.log("Selected item element:", itemElement);
                        setSelectedStack(itemStack, itemElement as HTMLElement);

                        // Prevent event bubbling
                        e.stopPropagation();
                    }}
                >
                    {!isDragging && <InventoryItem itemStack={itemStack} itemDef={itemDef} />}
                </div>
            </div>
        </>
    );
}, (prevProps: InventoryItemContentProps, nextProps: InventoryItemContentProps) => {
    // Custom equality function to prevent unnecessary re-renders
    // Return false to trigger a re-render when props change

    // Check visual state props
    if (prevProps.isOver !== nextProps.isOver) return false;
    if (prevProps.isDragging !== nextProps.isDragging) return false;

    // Always re-render when selection changes
    if (prevProps.isSelected !== nextProps.isSelected) {
        console.log("Selection changed:", prevProps.isSelected, "->", nextProps.isSelected);
        return false;
    }

    // Check if itemStack has changed
    if (!prevProps.itemStack && !nextProps.itemStack) return true;
    if (!prevProps.itemStack || !nextProps.itemStack) return false;

    const prevStack = prevProps.itemStack;
    const nextStack = nextProps.itemStack;

    // Check all relevant properties of itemStack
    if (prevStack.uuid !== nextStack.uuid) return false;
    if (prevStack.quantity !== nextStack.quantity) return false;
    if (prevStack.itemId !== nextStack.itemId) return false;

    // If we got here, the components are equal
    return true;
});
