import React, { useState, useEffect } from 'react';
import { getText } from '../i18n';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { Item, PlacedBuilding, CookingRecipe } from '../Interfaces';
import { useRootStore } from '../stores/rootStore';
import { ItemIcon } from './common';
import { startProgressBar } from '../gameinfo';
import { showPromptOverlay } from '../util';
import { COOKABLE_FOODS, FOOD } from '../enums/food_enums';
import { Items } from 'src/enums/resources';

interface CookingModalProps {
  building: PlacedBuilding;
  isOpen: boolean;
  onClose: () => void;
}

export const CookingModal: React.FC<CookingModalProps> = ({
  building,
  isOpen,
  onClose
}) => {
  const { itemStacks, addItemToInventory, removeItemsFromInventory, updateBuildingProperties } = useRootStore();
  const [cookingIngredients, setCookingIngredients] = useState<{ itemId: string, quantity: number }[]>([]);
  const [previewRecipe, setPreviewRecipe] = useState<CookingRecipe | null>(null);
  const [discoveredRecipes, setDiscoveredRecipes] = useState<string[]>([]);
  const [showRecipesModal, setShowRecipesModal] = useState(false);

  // Reset cooking ingredients when modal opens
  useEffect(() => {
    if (isOpen) {
      setCookingIngredients([]);
      setPreviewRecipe(null);

      // Load discovered recipes from localStorage
      const savedRecipes = localStorage.getItem('discoveredRecipes');
      if (savedRecipes) {
        setDiscoveredRecipes(JSON.parse(savedRecipes));
      }
    }
  }, [isOpen]);

  // Get all food items from inventory
  const foodItems = itemStacks.filter(stack => {
    if (!stack) return false;
    const food = FOOD[stack.itemId];
    return food !== undefined;
  });

  // console.log("foodItems", foodItems);

  // Calculate recipe preview based on ingredients
  useEffect(() => {
    if (cookingIngredients.length === 0) {
      setPreviewRecipe(null);
      return;
    }

    // Calculate total values
    let totalWaterVal = 0;
    let totalVegVal = 0;
    let totalFruitVal = 0;
    let totalMeatVal = 0;
    let totalSweetVal = 0;
    let totalFishVal = 0;

    cookingIngredients.forEach(ingredient => {
      // const itemDef = itemStacks.find(stack => stack && stack.itemId === ingredient.itemId);
      const itemDef = FOOD[ingredient.itemId];
      if (itemDef) {
        if (itemDef['waterVal']) totalWaterVal += itemDef['waterVal'] * ingredient.quantity;
        if (itemDef['vegVal']) totalVegVal += itemDef['vegVal'] * ingredient.quantity;
        if (itemDef['fruitVal']) totalFruitVal += itemDef['fruitVal'] * ingredient.quantity;
        if (itemDef['meatVal']) totalMeatVal += itemDef['meatVal'] * ingredient.quantity;
        if (itemDef['sweetVal']) totalSweetVal += itemDef['sweetVal'] * ingredient.quantity;
        if (itemDef['fishVal']) totalFishVal += itemDef['fishVal'] * ingredient.quantity;
      }
    });

    console.log("totalWaterVal", totalWaterVal);
    console.log("totalMeatVal", totalMeatVal);

    // Find matching recipe
    let matchedRecipe: CookingRecipe | null = null;

    for (const recipeId in COOKABLE_FOODS) {
      const recipe = COOKABLE_FOODS[recipeId] as CookingRecipe;

      // Skip MysterySoup as it's the fallback
      if (recipe.id === 'MysterySoup') continue;

      let isMatch = true;

      // Check if all required values are met
      if (recipe.requiredWaterVal && totalWaterVal < recipe.requiredWaterVal) isMatch = false;
      if (recipe.requiredVegVal && totalVegVal < recipe.requiredVegVal) isMatch = false;
      if (recipe.requiredFruitVal && totalFruitVal < recipe.requiredFruitVal) isMatch = false;
      if (recipe.requiredMeatVal && totalMeatVal < recipe.requiredMeatVal) isMatch = false;
      if (recipe.requiredSweetVal && totalSweetVal < recipe.requiredSweetVal) isMatch = false;
      if (recipe.requiredFishVal && totalFishVal < recipe.requiredFishVal) isMatch = false;

      if (isMatch) {
        matchedRecipe = recipe;
        break;
      }
    }

    // If no recipe matched, use MysterySoup
    if (!matchedRecipe) {
      matchedRecipe = COOKABLE_FOODS['MysterySoup'] as CookingRecipe;
    }

    setPreviewRecipe(matchedRecipe);
  }, [cookingIngredients, itemStacks]);

  // Add ingredient to the cooking pot
  const addIngredient = (itemId: string) => {
    // Limit to 9 total ingredients (3x3 grid)
    if (cookingIngredients.reduce((total, item) => total + item.quantity, 0) >= 9) {
      return;
    }

    const existingItem = cookingIngredients.find(item => item.itemId === itemId);

    if (existingItem) {
      setCookingIngredients(prev =>
        prev.map(item =>
          item.itemId === itemId
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      );
    } else {
      setCookingIngredients(prev => [...prev, { itemId, quantity: 1 }]);
    }
  };

  // Remove ingredient from the cooking pot
  const removeIngredient = (itemId: string) => {
    const existingItem = cookingIngredients.find(item => item.itemId === itemId);

    if (existingItem && existingItem.quantity > 1) {
      setCookingIngredients(prev =>
        prev.map(item =>
          item.itemId === itemId
            ? { ...item, quantity: item.quantity - 1 }
            : item
        )
      );
    } else {
      setCookingIngredients(prev => prev.filter(item => item.itemId !== itemId));
    }
  };

  // Cook the food
  const cook = () => {
    if (cookingIngredients.length === 0 || !previewRecipe) return;

    startProgressBar(getText('Cooking'), 30, () => {
      // Remove ingredients from inventory
      const itemsToRemove = cookingIngredients.map(item => ({
        itemDef: Items[item.itemId],
        quantity: item.quantity
      }));

      removeItemsFromInventory(itemsToRemove);

      // Add cooked food to inventory
      addItemToInventory(previewRecipe);

      // Reduce fire duration (cooking consumes some fire)
      if (building.fireDuration) {
        const newFireDuration = Math.max(0, building.fireDuration - 5);

        // If fire duration reaches 0, extinguish the fire
        if (newFireDuration <= 0) {
          updateBuildingProperties(building, {
            fireDuration: 0,
            isLit: false
          });
          showPromptOverlay(getText('The fire has gone out'));
        } else {
          updateBuildingProperties(building, {
            fireDuration: newFireDuration
          });
        }
      }

      // If this is a new recipe discovery, save it
      if (previewRecipe.id !== 'MysterySoup' && !discoveredRecipes.includes(previewRecipe.id)) {
        const newDiscoveredRecipes = [...discoveredRecipes, previewRecipe.id];
        setDiscoveredRecipes(newDiscoveredRecipes);
        localStorage.setItem('discoveredRecipes', JSON.stringify(newDiscoveredRecipes));

        // Show discovery message
        showPromptOverlay(getText('New recipe discovered') + `: ${previewRecipe.name}`);
      } else {
        // Show regular cooking message
        showPromptOverlay(getText('Cooked') + `: ${previewRecipe.name}`);
      }

      // Reset cooking ingredients
      setCookingIngredients([]);
      setPreviewRecipe(null);
      onClose();
    });
  };

  return (
    <MacOSModal
      title={getText('Cooking')}
      isOpen={isOpen}
      onClose={onClose}
      initialSize={{ width: 700, height: 600 }}
    >
      <div className="cooking-modal">
        <div className="cooking-controls">
          <button
            className="recipes-button"
            onClick={() => setShowRecipesModal(true)}
          >
            {getText('Recipes')}
          </button>
        </div>

        <h3>{getText('Available Food')}</h3>
        <div className="inventory-grid">
          {foodItems.map((stack, index) => (
            <div
              key={index}
              className="inventory-item"
              onClick={() => addIngredient(stack.itemId)}
            >
              <ItemIcon itemDef={Items[stack.itemId]} />
              <div className="item-quantity">{stack.quantity}</div>
            </div>
          ))}
        </div>

        <h3>{getText('Cooking Pot')}</h3>
        <div className="cooking-grid">
          {Array(9).fill(null).map((_, index) => {
            // Find an ingredient to display in this slot
            let displayIngredient = null;
            let remainingQuantity = index + 1;

            for (const ingredient of cookingIngredients) {
              if (remainingQuantity <= ingredient.quantity) {
                displayIngredient = ingredient.itemId;
                break;
              }
              remainingQuantity -= ingredient.quantity;
            }

            return (
              <div
                key={index}
                className="cooking-slot"
                onClick={() => displayIngredient && removeIngredient(displayIngredient)}
              >
                {displayIngredient && <ItemIcon itemDef={Items[displayIngredient]} />}
              </div>
            );
          })}
        </div>

        {previewRecipe && (
          <div className="recipe-preview">
            <h3>{getText('Cooking Result')}</h3>
            <div className="recipe-result">
              <div className="result-icon">
                <ItemIcon itemDef={previewRecipe} />
              </div>
              <div className="result-info">
                <div className="result-name">{previewRecipe.name}</div>
                <div className="result-description">{previewRecipe.description}</div>
                <div className="result-stats">
                  {previewRecipe.food > 0 && <div>🍖 {getText('Food')}: +{previewRecipe.food}</div>}
                  {previewRecipe.water > 0 && <div>💧 {getText('Water')}: +{previewRecipe.water}</div>}
                  {previewRecipe.energy > 0 && <div>⚡ {getText('Energy')}: +{previewRecipe.energy}</div>}
                  {previewRecipe.health > 0 && <div>❤️ {getText('Health')}: +{previewRecipe.health}</div>}
                </div>
              </div>
            </div>
          </div>
        )}

        <button
          className="cook-button"
          disabled={cookingIngredients.length === 0}
          onClick={cook}
        >
          {getText('Cook')}
        </button>
      </div>

      {/* Recipes Modal */}
      <MacOSModal
        title={getText('Discovered Recipes')}
        isOpen={showRecipesModal}
        onClose={() => setShowRecipesModal(false)}
        initialSize={{ width: 500, height: 400 }}
      >
        <div className="recipes-modal">
          <h3>{getText('Known Recipes')}</h3>
          <div className="recipes-list">
            {discoveredRecipes.length === 0 ? (
              <p>{getText('No recipes discovered yet. Try experimenting with different ingredients!')}</p>
            ) : (
              <ul>
                {discoveredRecipes.map(recipeId => {
                  const recipe = COOKABLE_FOODS[recipeId] as CookingRecipe;
                  return (
                    <li key={recipeId} className="recipe-item">
                      <div className="recipe-icon">
                        <ItemIcon itemDef={recipe} />
                      </div>
                      <div className="recipe-info">
                        <div className="recipe-name">{recipe.name}</div>
                        <div className="recipe-requirements">
                          {recipe.requiredWaterVal && <span>💧 {recipe.requiredWaterVal}</span>}
                          {recipe.requiredVegVal && <span>🥬 {recipe.requiredVegVal}</span>}
                          {recipe.requiredFruitVal && <span>🍎 {recipe.requiredFruitVal}</span>}
                          {recipe.requiredMeatVal && <span>🍖 {recipe.requiredMeatVal}</span>}
                          {recipe.requiredSweetVal && <span>🍯 {recipe.requiredSweetVal}</span>}
                          {recipe.requiredFishVal && <span>🐟 {recipe.requiredFishVal}</span>}
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
        </div>
      </MacOSModal>
    </MacOSModal>
  );
};
