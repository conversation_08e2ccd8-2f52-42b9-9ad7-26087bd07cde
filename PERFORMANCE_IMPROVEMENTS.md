# Fog of War Performance Improvements

## Problem Statement
The original fog of war implementation had severe performance issues that would worsen as the player explored more areas:

### Original Issues:
1. **O(n) exploration checks** - `isExplored()` performed linear search through up to 1000 explored areas
2. **Redundant fog regeneration** - Full canvas redraw on every player movement frame
3. **Complex rendering** - Expensive gradient circles with noise-based irregular boundaries  
4. **Memory growth** - Stored overlapping circular areas indefinitely
5. **No spatial optimization** - No deduplication of explored areas

## Solution: Grid-Based Spatial Indexing

### Key Optimizations:

#### 1. **O(1) Exploration Lookups**
- Replaced array of circles with 2D boolean grid
- Each grid cell represents 32x32 world units
- `isExplored(x, y)` now takes constant time regardless of exploration size

#### 2. **Incremental Updates**
- Only updates fog when player enters new grid cell
- Tracks `lastPlayerGridX/Y` to avoid redundant work
- Skips processing if player hasn't moved to new cell

#### 3. **Simple Rectangle Rendering**
- Replaced complex gradient circles with fast `fillRect()` operations
- Removed expensive noise-based irregular boundaries
- Eliminated gradient creation overhead

#### 4. **Fixed Memory Usage**
- Grid size is constant: `ceil(worldWidth/32) × ceil(worldHeight/32)`
- No more growing arrays of explored areas
- Memory usage is predictable and bounded

#### 5. **Smart Caching**
- `needsFullRedraw` flag prevents unnecessary redraws
- Only regenerates fog when new areas are actually explored

## Performance Comparison

### Before (Inefficient):
```typescript
// O(n) lookup - gets slower as exploration grows
public isExplored(x: number, y: number): boolean {
    for (const area of this.exploredAreas) { // Up to 1000 iterations
        const dx = x - area.x;
        const dy = y - area.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance <= area.radius) return true;
    }
    return false;
}

// Called every frame during movement
public updateExploration(playerX: number, playerY: number): void {
    this.exploredAreas.push({...}); // Always adds new area
    this.regenerateFog(); // Always redraws entire canvas
}
```

### After (Optimized):
```typescript
// O(1) lookup - constant time regardless of exploration
public isExplored(x: number, y: number): boolean {
    const gridX = Math.floor(x / this.gridSize);
    const gridY = Math.floor(y / this.gridSize);
    return this.exploredGrid[gridY][gridX]; // Single array access
}

// Only processes when entering new grid cell
public updateExploration(playerX: number, playerY: number): void {
    const gridX = Math.floor(playerX / this.gridSize);
    const gridY = Math.floor(playerY / this.gridSize);
    
    if (gridX === this.lastPlayerGridX && gridY === this.lastPlayerGridY) {
        return; // Skip if same cell
    }
    // ... only update when necessary
}
```

## Expected Performance Gains

### Memory Usage:
- **Before**: O(n) where n = number of explored areas (up to 1000)
- **After**: O(1) fixed grid size based on world dimensions

### Exploration Checks:
- **Before**: O(n) linear search through explored areas
- **After**: O(1) direct grid lookup

### Fog Regeneration:
- **Before**: Every frame during movement (60+ FPS)
- **After**: Only when entering new grid cells (~1-2 times per second)

### Canvas Operations:
- **Before**: Complex gradient circles with noise calculations
- **After**: Simple rectangle fills

## Implementation Details

### Grid Configuration:
- **Grid cell size**: 32x32 world units
- **Visibility radius**: 80 units (covers ~2.5 grid cells)
- **Memory per cell**: 1 bit (boolean)

### For a medium world (2111x2111):
- **Grid dimensions**: 66x66 cells = 4,356 cells
- **Memory usage**: ~545 bytes for exploration data
- **Max fog redraws**: ~66 times to cross entire world

This represents a **massive performance improvement** especially for longer play sessions where players have explored large areas.
