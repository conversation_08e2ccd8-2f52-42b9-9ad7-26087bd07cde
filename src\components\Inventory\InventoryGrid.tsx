import React = require("react");
import { InventoryItemStack } from "src/Interfaces";
import { InventoryItemSlot } from "./InventoryItemSlot";
import { useRootStore } from "src/stores/rootStore";
import { DEFAULT_INV_SLOT } from "src/settings";
import { useEquipmentStore } from "src/stores/equipmentStore";

interface InventoryGridProps {
    itemStacks: InventoryItemStack[];
    selectedStack: InventoryItemStack | null;
    setSelectedStack: (itemStack: InventoryItemStack) => void;
    activeId: string | null;
}

export const InventoryGrid = React.memo(({
    itemStacks,
    selectedStack,
    setSelectedStack,
    activeId
}: InventoryGridProps) => {

    const equippedItems = useEquipmentStore(state => state.equippedItems);
    // console.log("selectedStack ", selectedStack?.uuid === itemStacks[9]?.uuid);

    return (
        <div className="inventory-grid">
            {new Array(DEFAULT_INV_SLOT + (equippedItems.BACKPACK?.itemDef.storageCapacity ?? 0)).fill(null).map((_, index) => (
                <InventoryItemSlot
                    key={index}
                    itemStack={itemStacks[index]}
                    stackIndex={index}
                    isSelected={itemStacks[index] && selectedStack?.uuid === itemStacks[index].uuid}
                    setSelectedStack={setSelectedStack}
                    isActive={`draggable-${index}` === activeId}
                />
            ))}
        </div>
    );
});
