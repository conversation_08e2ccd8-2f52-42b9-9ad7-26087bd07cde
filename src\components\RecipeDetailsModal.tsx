import React from 'react';
import { getText } from '../i18n';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { CraftableItem } from '../Interfaces';
import { Items } from '../enums/resources';
import { ItemIcon } from './common';

interface RecipeDetailsModalProps {
  recipe: CraftableItem;
  isOpen: boolean;
  onClose: () => void;
  canCraft: boolean;
  getIngredientQuantity: (ingredientId: string) => number;
  onCraft: () => void;
}

export const RecipeDetailsModal: React.FC<RecipeDetailsModalProps> = ({
  recipe,
  isOpen,
  onClose,
  canCraft,
  getIngredientQuantity,
  onCraft
}) => {
  if (!recipe) return null;

  return (
    <MacOSModal
      title={recipe.name}
      isOpen={isOpen}
      onClose={onClose}
      initialSize={{ width: 500, height: 550 }}
    >
      <div className="recipe-details-modal">
        <div className="recipe-detail-icon">
          <ItemIcon itemDef={recipe} />
        </div>
        <p>{recipe.description}</p>
        <h4>{getText('Ingredients')}:</h4>
        <ul className="ingredient-list">
          {recipe.ingredients?.map((ingredient) => {
            const available = getIngredientQuantity(ingredient.itemDef.id);
            const hasEnough = available >= ingredient.quantity;
            return (
              <li key={ingredient.itemDef.id} className={hasEnough ? 'has-enough' : 'not-enough'}>
                {Items[ingredient.itemDef.id]?.name} ({available}/{ingredient.quantity})
              </li>
            );
          })}
        </ul>
        <h4>{getText('Result')}:</h4>
        <div className="recipe-result">
          <div className="result-icon">
            <ItemIcon itemDef={recipe} />
          </div>
          <div className="result-info">
            <div className="result-name">{recipe.name}</div>
            <div className="result-description">{recipe.description}</div>
            {recipe.type.name === "Edible" && (
              <div className="result-stats">
                {recipe.food > 0 && <div>🍖 {getText('Food')}: +{recipe.food}</div>}
                {recipe.water > 0 && <div>💧 {getText('Water')}: +{recipe.water}</div>}
                {recipe.energy > 0 && <div>⚡ {getText('Energy')}: +{recipe.energy}</div>}
                {recipe.health > 0 && <div>❤️ {getText('Health')}: +{recipe.health}</div>}
              </div>
            )}
            {recipe.type.name === "Medicinal" && (
              <div className="result-stats">
                <div>❤️ {getText('Health')}: +{recipe.health}</div>
              </div>
            )}
          </div>
        </div>
        <button
          className="craft-button green-btn"
          disabled={!canCraft}
          onClick={onCraft}
        >
          {getText('Craft')}
        </button>
      </div>
    </MacOSModal>
  );
};
