// Internationalization support for mapgen2

export const Languages = {
    en: "English",
    zh: "中文"
};

// Default language
export let currentLanguage = "zh";

// Set language function
export function setLanguage(lang) {
    if (Languages[lang]) {
        currentLanguage = lang;
        // Update all UI elements with new language
        updateAllUIText();
        return true;
    }
    return false;
}

// Get text based on current language
export function getText(key) {
    if (!Translations[key]) {
        // console.warn(`Translation key not found: ${key}`);
        return key;
    }

    return Translations[key][currentLanguage] || Translations[key].en;
}


export function getTextWithArgs(key, kvPairs) {
    if (!TranslationsWithArgs[key]) {
        return key;
    }

    let txt: string = TranslationsWithArgs[key][currentLanguage] || Translations[key].en;
    for (const k in kvPairs) {
        txt = txt.replace(`{${k}}`, kvPairs[k]);
    }

    return txt;
}

// Update all UI text elements
function updateAllUIText() {
    // Update all elements with data-i18n attribute
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        element.textContent = getText(key);
    });

    // Redraw any dynamic content that contains text
    // if (window.updateInventoryDisplay) window.updateInventoryDisplay();
    // if (window.updateRecipeList) window.updateRecipeList();
    // if (window.updateGameStatus) window.updateGameStatus();
    // if (window.updateWeatherDisplay) window.updateWeatherDisplay();
}

const FISH_TRANSLATIONS = {
    "FISH": {
        en: "Fish",
        zh: "鱼"
    },
    "SEAWEED": {
        en: "Seaweed",
        zh: "海藻"
    },
    "Tuna": {
        en: "Tuna",
        zh: "金枪鱼"
    },
    "Mackerel": {
        en: "Mackerel",
        zh: "鲭鱼"
    },
    "Sardine": {
        en: "Sardine",
        zh: "沙丁鱼"
    },
    "Squid": {
        en: "Squid",
        zh: "鱿鱼"
    },
    "Swordfish": {
        en: "Swordfish",
        zh: "剑鱼"
    },
    "Trout": {
        en: "Trout",
        zh: "鳟鱼"
    },
    "Bass": {
        en: "Bass",
        zh: "鲈鱼"
    },
    "Carp": {
        en: "Carp",
        zh: "鲤鱼"
    },
    "Perch": {
        en: "Perch",
        zh: "河鲈"
    },
    "Catfish": {
        en: "Catfish",
        zh: "鲶鱼"
    },
    "Eel": {
        en: "Eel",
        zh: "鳗鱼"
    },
    "Frog": {
        en: "Frog",
        zh: "青蛙"
    },
    "Snakehead": {
        en: "Snakehead",
        zh: "蛇头鱼"
    },
    "Crab": {
        en: "Crab",
        zh: "螃蟹"
    },
    "Clam": {
        en: "Clam",
        zh: "蛤蜊"
    },
    "Shrimp": {
        en: "Shrimp",
        zh: "虾"
    },
    "Lobster": {
        en: "Lobster",
        zh: "龙虾"
    },
    "Salmon": {
        en: "Salmon",
        zh: "鲑鱼"
    },
    "Pike": {
        en: "Pike",
        zh: "梭鱼"
    },
    "Sturgeon": {
        en: "Sturgeon",
        zh: "鲟鱼"
    },
    "Minnow": {
        en: "Minnow",
        zh: "鲦鱼"
    },
}

const BIOMES_TRANSLATIONS = {
    "Terrain": {
        en: "Terrain",
        zh: "地形"
    },
    "OCEAN": {
        en: "Ocean",
        zh: "海洋"
    },
    "MARSH": {
        en: "Marsh",
        zh: "沼泽"
    },
    "LAKE": {
        en: "Lake",
        zh: "湖泊"
    },
    "ICEFIELD": {
        en: "Ice",
        zh: "冰"
    },
    "SNOWFIELD": {
        en: "Snow",
        zh: "雪"
    },
    "BEACH": {
        en: "Beach",
        zh: "海滩"
    },
    "TUNDRA": {
        en: "Tundra",
        zh: "冻原"
    },
    "TEMPERATE_DESERT": {
        en: "Temperate Desert",
        zh: "温带沙漠"
    },
    "SUBTROPICAL_DESERT": {
        en: "Subtropical Desert",
        zh: "亚热带沙漠"
    },
    "TEMPERATE_RAIN_FOREST": {
        en: "Temperate Rain Forest",
        zh: "温带雨林"
    },
    "TROPICAL_RAIN_FOREST": {
        en: "Tropical Rain Forest",
        zh: "热带雨林"
    },
    "TEMPERATE_DECIDUOUS_FOREST": {
        en: "Temperate Deciduous Forest",
        zh: "温带落叶森林"
    },
    "GRASSLAND": {
        en: "Grassland",
        zh: "草地"
    },
    "TROPICAL_SEASONAL_FOREST": {
        en: "Tropical Seasonal Forest",
        zh: "热带季节性森林"
    },
    "TAIGA": {
        en: "Taiga",
        zh: "针叶林"
    },
    "SHRUBLAND": {
        en: "Shrubland",
        zh: "灌木林"
    },
    "VOLCANIC_WASTELAND": {
        en: "Volcanic Wasteland",
        zh: "焦土"
    },
    "WASTELAND": {
        en: "Wasteland",
        zh: "荒地"
    },
}

export const DESCRIPTIONS = {
    // Resource descriptions
    "resource_desc_edible": {
        en: "Can be consumed for sustenance",
        zh: "可以食用以维持生命"
    },
    "resource_desc_material": {
        en: "Used for crafting",
        zh: "用于制作物品"
    },
    "resource_desc_tool": {
        en: "Helps with various tasks",
        zh: "帮助完成各种任务"
    },
    "resource_desc_valuable": {
        en: "Can be traded or sold",
        zh: "可以交易或出售"
    },
    "resource_desc_medicinal": {
        en: "Provides healing effects",
        zh: "提供治疗效果"
    },
    "resource_desc_harvestable": {
        en: "Can be harvested for resources",
        zh: "可以收获资源"
    },
    "resource_desc_equipable": {
        en: "Can be equipped for various tasks",
        zh: "可以装备用于各种任务"
    },
    "resource_desc_unknown": {
        en: "Unknown resource type",
        zh: "未知资源类型"
    },
    "resource_desc_huntable": {
        en: "Can be hunted for resources",
        zh: "可以被猎杀以获取资源"
    },


    "desc_small_backpack": {
        en: "A small backpack that increases inventory capacity by 5 slots",
        zh: "一个增加5个物品栏位的小背包"
    },
    "desc_medium_backpack": {
        en: "A medium backpack that increases inventory capacity by 10 slots",
        zh: "一个增加10个物品栏位的中背包"
    },
    "desc_large_backpack": {
        en: "A large backpack that increases inventory capacity by 20 slots",
        zh: "一个增加20个物品栏位的大背包"
    },
    "desc_fish": {
        en: "Fresh seafood rich in protein",
        zh: "富含蛋白质的新鲜海鲜"
    },
    "desc_seaweed": {
        en: "Nutritious aquatic plant",
        zh: "营养丰富的水生植物"
    },
    "desc_Tuna": {
        en: "A large, oily fish with firm flesh and rich flavor",
        zh: "一种肉质紧实、风味浓郁的大型油性鱼"
    },
    "desc_Mackerel": {
        en: "A medium-sized fish with distinctive markings and oily flesh",
        zh: "一种有着独特花纹和油性肉质的中型鱼"
    },
    "desc_Sardine": {
        en: "A small, oily fish that travels in large schools",
        zh: "一种成群游动的小型油性鱼"
    },
    "desc_Squid": {
        en: "A fast-swimming cephalopod with ten arms and a soft body",
        zh: "一种有十只触手和柔软身体的快速游动的头足类动物"
    },
    "desc_Swordfish": {
        en: "A large predatory fish with a long, flat bill",
        zh: "一种有着长而扁平的喙的大型掠食鱼"
    },
    "desc_Trout": {
        en: "A freshwater fish related to salmon with delicate flesh",
        zh: "一种与鲑鱼相关的肉质细嫩的淡水鱼"
    },
    "desc_Bass": {
        en: "A popular game fish with firm white flesh",
        zh: "一种肉质紧实、肉色洁白的受欢迎的游钓鱼"
    },
    "desc_Carp": {
        en: "A hardy freshwater fish with scales and whiskers",
        zh: "一种有鳞片和胡须的耐寒淡水鱼"
    },
    "desc_Perch": {
        en: "A small freshwater fish with spiny fins",
        zh: "一种有刺鳍的小型淡水鱼"
    },
    "desc_Catfish": {
        en: "A bottom-dwelling fish with whisker-like barbels",
        zh: "一种有胡须状须的底栖鱼"
    },
    "desc_Eel": {
        en: "A snake-like fish with elongated body and smooth skin",
        zh: "一种身体细长、皮肤光滑的蛇形鱼"
    },
    "desc_Frog": {
        en: "An amphibian with strong hind legs for jumping",
        zh: "一种有强壮后腿用于跳跃的两栖动物"
    },
    "desc_Snakehead": {
        en: "An aggressive predatory fish that can breathe air",
        zh: "一种能呼吸空气的凶猛掠食鱼"
    },
    "desc_Crab": {
        en: "A crustacean with a hard shell and pincers",
        zh: "一种有硬壳和钳子的甲壳动物"
    },
    "desc_Clam": {
        en: "A bivalve mollusk with a hinged shell",
        zh: "一种有铰链壳的双壳类软体动物"
    },
    "desc_Shrimp": {
        en: "A small crustacean with a translucent body",
        zh: "一种身体半透明的小型甲壳动物"
    },
    "desc_Lobster": {
        en: "A large marine crustacean with powerful claws",
        zh: "一种有强壮钳子的大型海洋甲壳动物"
    },
    "desc_Salmon": {
        en: "A migratory fish known for swimming upstream to spawn",
        zh: "一种以逆流而上产卵而闻名的洄游鱼"
    },
    "desc_Pike": {
        en: "A predatory freshwater fish with a long, flat snout",
        zh: "一种有着长而扁平吻部的淡水掠食鱼"
    },
    "desc_Sturgeon": {
        en: "An ancient fish with bony plates and no scales",
        zh: "一种有骨板而无鳞片的古老鱼类"
    },
    "desc_Minnow": {
        en: "A tiny fish often used as bait",
        zh: "一种常被用作鱼饵的微小鱼类"
    },
    "desc_unknown": {
        en: "Please explore this item first",
        zh: "请先探索这个物品"
    },
}

// Translation dictionary
// Helper function to add translations
function addTranslation(key, translations) {
    Translations[key] = translations;
}


export const WEATHER_TRANSLATIONS = {
    "weather_effects_title": {
        en: "Player Effects",
        zh: "玩家效果"
    },
    "weather_resource_effects_title": {
        en: "Resource Effects",
        zh: "资源效果"
    },
    "weather_thirst_rate": {
        en: "Thirst Rate",
        zh: "口渴速率"
    },
    "weather_hunger_rate": {
        en: "Hunger Rate",
        zh: "饥饿速率"
    },
    "weather_energy_consumption": {
        en: "Energy Consumption",
        zh: "能量消耗"
    },
    "weather_movement_speed": {
        en: "Movement Speed",
        zh: "移动速度"
    },
    "weather_health_risk": {
        en: "Health Risk",
        zh: "健康风险"
    },
    "weather_gathering_speed": {
        en: "Gathering Speed",
        zh: "采集速度"
    },
    "weather_berry_find_rate": {
        en: "Berry Find Rate",
        zh: "浆果发现率"
    },
    "weather_mushroom_find_rate": {
        en: "Mushroom Find Rate",
        zh: "蘑菇发现率"
    },
    "weather_effect_slower": {
        en: "{value}% slower",
        zh: "{value}% 更慢"
    },
    "weather_effect_faster": {
        en: "{value}% faster",
        zh: "{value}% 更快"
    },
    "weather_effect_lower": {
        en: "{value}% lower",
        zh: "{value}% 更低"
    },
    "weather_effect_higher": {
        en: "{value}% higher",
        zh: "{value}% 更高"
    },
    "weather_effect_health_risk": {
        en: "{value}% chance of damage per minute",
        zh: "每分钟{value}%几率受到伤害"
    }
};

const TranslationsWithArgs = {
    "seasonDaysLeft": {
        en: "{day} days left",
        zh: "还剩 {day} 天"
    },
    ...WEATHER_TRANSLATIONS
}


export const BOAT_TRANSLATIONS = {
    "boat_required": {
        en: "You need a boat to travel on water",
        zh: "你需要一艘船才能在水上航行"
    },
    "select_boat": {
        en: "Select a boat to use",
        zh: "选择要使用的船"
    },
    "boat_raft": {
        en: "Simple Raft",
        zh: "简易木筏"
    },
    "boat_canoe": {
        en: "Wooden Canoe",
        zh: "木制独木舟"
    },
    "boat_fishing_boat": {
        en: "Fishing Boat",
        zh: "渔船"
    },
    "boat_sailboat": {
        en: "Sailboat",
        zh: "帆船"
    },
    "desc_boat_raft": {
        en: "A basic raft made of logs. Low durability but better than swimming.",
        zh: "用原木制成的基础木筏。耐久度低但总比游泳好。"
    },
    "desc_boat_canoe": {
        en: "A lightweight canoe carved from a single tree trunk. Good maneuverability but limited capacity.",
        zh: "由单个树干雕刻而成的轻型独木舟。机动性好但容量有限。"
    },
    "desc_boat_fishing_boat": {
        en: "A sturdy fishing boat with good capacity and durability. Moderate speed.",
        zh: "一艘结实的渔船，具有良好的容量和耐久度。速度适中。"
    },
    "desc_boat_sailboat": {
        en: "A well-crafted sailboat with excellent speed and durability. Best thermal protection.",
        zh: "一艘精心制作的帆船，具有出色的速度和耐久度。最佳隔热保护。"
    },
    "boat_stat_durability": {
        en: "Durability",
        zh: "耐久度"
    },
    "boat_stat_capacity": {
        en: "Weight Capacity",
        zh: "载重能力"
    },
    "boat_stat_weight": {
        en: "Weight",
        zh: "重量"
    },
    "boat_stat_speed": {
        en: "Speed",
        zh: "速度"
    },
    "boat_stat_waterproof": {
        en: "Waterproof",
        zh: "防水性"
    },
    "boat_stat_insulation": {
        en: "Insulation",
        zh: "隔热效果"
    },
    "boat_stat_thermal": {
        en: "Thermal Shield",
        zh: "热防护"
    }
};


export const Translations = {
    "AVAILABLE RESOURCES": {
        en: "AVAILABLE RESOURCES",
        zh: "可获取资源"
    },
    "AVAILABLE ACTIVITIES": {
        en: "AVAILABLE ACTIVITIES",
        zh: "可进行的活动"
    },
    "Explore&Gather": {
        en: "Explore & Gather",
        zh: "探索 & 采集"
    },
    "Inventory": {
        en: "Inventory",
        zh: "背包"
    },
    "Search This Area": {
        en: "Search This Area",
        zh: "搜索这个区域"
    },
    "Leaf": {
        en: "Leaf",
        zh: "树叶"
    },
    "Bark": {
        en: "Bark",
        zh: "树皮"
    },
    "Grass": {
        en: "Grass",
        zh: "草"
    },
    // Building system
    "Build": {
        en: "Build",
        zh: "建造"
    },
    "Building": {
        en: "Building",
        zh: "建筑"
    },
    "Available Buildings": {
        en: "Available Buildings",
        zh: "可用建筑"
    },
    "Building Grid": {
        en: "Building Grid",
        zh: "建筑网格"
    },
    "Cancel": {
        en: "Cancel",
        zh: "取消"
    },
    "Place Building": {
        en: "Place Building",
        zh: "放置建筑"
    },
    "Not Enough Resources": {
        en: "Not Enough Resources",
        zh: "资源不足"
    },
    "Close": {
        en: "Close",
        zh: "关闭"
    },
    "building_small_house": {
        en: "Small House",
        zh: "小屋"
    },
    "building_small_house_desc": {
        en: "A small shelter to rest and regain energy",
        zh: "一个小避难所，可以休息和恢复精力"
    },
    "building_fire_pit": {
        en: "Fire Pit",
        zh: "火堆"
    },
    "building_fire_pit_desc": {
        en: "A place to cook food and stay warm",
        zh: "一个可以烹饪食物和保暖的地方"
    },
    "building_storage_box": {
        en: "Storage Box",
        zh: "储物箱"
    },
    "building_storage_box_desc": {
        en: "Store your items safely",
        zh: "安全地存储你的物品"
    },
    "building_garden_plot": {
        en: "Garden Plot",
        zh: "花园地块"
    },
    "building_garden_plot_desc": {
        en: "Grow plants and vegetables",
        zh: "种植植物和蔬菜"
    },
    "interaction_sleep": {
        en: "Sleep",
        zh: "睡觉"
    },
    "interaction_cook": {
        en: "Cook",
        zh: "烹饪"
    },
    "interaction_store": {
        en: "Store Items",
        zh: "存储物品"
    },
    "Storage": {
        en: "Storage",
        zh: "储存"
    },
    "interaction_plant": {
        en: "Plant",
        zh: "种植"
    },
    "interaction_harvest": {
        en: "Harvest",
        zh: "收获"
    },
    "interaction_light_fire": {
        en: "Light Fire",
        zh: "点火"
    },
    "Sleeping": {
        en: "Sleeping",
        zh: "睡觉中"
    },
    "Harvesting": {
        en: "Harvesting",
        zh: "收获中"
    },
    "Lighting Fire": {
        en: "Lighting Fire",
        zh: "点火中"
    },
    "Cooking": {
        en: "Cooking",
        zh: "烹饪中"
    },
    "Fire lit!": {
        en: "Fire lit!",
        zh: "火已点燃！"
    },
    "The fire has gone out": {
        en: "The fire has gone out",
        zh: "火已熄灭"
    },
    "Fire is burning": {
        en: "Fire is burning",
        zh: "火正在燃烧"
    },
    "Remaining time": {
        en: "Remaining time",
        zh: "剩余时间"
    },
    "Cooked": {
        en: "Cooked",
        zh: "烹饪完成"
    },
    "New recipe discovered": {
        en: "New recipe discovered",
        zh: "发现新食谱"
    },
    "Available Flammables": {
        en: "Available Flammables",
        zh: "可用燃料"
    },
    "Added Flammables": {
        en: "Added Flammables",
        zh: "已添加燃料"
    },
    "Fire Duration": {
        en: "Fire Duration",
        zh: "火焰持续时间"
    },
    "minutes": {
        en: "minutes",
        zh: "分钟"
    },
    "Light Fire": {
        en: "Light Fire",
        zh: "点火"
    },
    "Available Food": {
        en: "Available Food",
        zh: "可用食物"
    },
    "Cooking Pot": {
        en: "Cooking Pot",
        zh: "烹饪锅"
    },
    "Cooking Result": {
        en: "Cooking Result",
        zh: "烹饪结果"
    },
    "Cook": {
        en: "Cook",
        zh: "烹饪"
    },
    "Recipes": {
        en: "Recipes",
        zh: "食谱"
    },
    "Discovered Recipes": {
        en: "Discovered Recipes",
        zh: "已发现食谱"
    },
    "Known Recipes": {
        en: "Known Recipes",
        zh: "已知食谱"
    },
    "No recipes discovered yet. Try experimenting with different ingredients!": {
        en: "No recipes discovered yet. Try experimenting with different ingredients!",
        zh: "尚未发现食谱。尝试使用不同的食材进行实验！"
    },

    // Cooking recipes
    "VegetableSoup": {
        en: "Vegetable Soup",
        zh: "蔬菜汤"
    },
    "desc_VegetableSoup": {
        en: "A hearty soup made with fresh vegetables",
        zh: "用新鲜蔬菜制成的美味汤"
    },
    "FruitSalad": {
        en: "Fruit Salad",
        zh: "水果沙拉"
    },
    "desc_FruitSalad": {
        en: "A refreshing mix of various fruits",
        zh: "各种水果的清爽混合"
    },
    "MeatStew": {
        en: "Meat Stew",
        zh: "肉炖"
    },
    "desc_MeatStew": {
        en: "A filling stew with tender meat",
        zh: "一种含有嫩肉的饱腹炖菜"
    },
    "FishSoup": {
        en: "Fish Soup",
        zh: "鱼汤"
    },
    "desc_FishSoup": {
        en: "A delicate soup made with fresh fish",
        zh: "用新鲜鱼制成的美味汤"
    },
    "SweetFruitCompote": {
        en: "Sweet Fruit Compote",
        zh: "甜水果蜜饯"
    },
    "desc_SweetFruitCompote": {
        en: "A sweet dessert made with fruits and honey",
        zh: "用水果和蜂蜜制成的甜点"
    },
    "MeatAndVegetableStew": {
        en: "Meat and Vegetable Stew",
        zh: "肉蔬炖"
    },
    "desc_MeatAndVegetableStew": {
        en: "A hearty stew with meat and vegetables",
        zh: "一种含有肉和蔬菜的饱腹炖菜"
    },
    "MysterySoup": {
        en: "Mystery Soup",
        zh: "神秘汤"
    },
    "desc_MysterySoup": {
        en: "A strange concoction with unpredictable effects",
        zh: "一种具有不可预测效果的奇怪混合物"
    },

    // Fishing system
    "Fishing": {
        en: "Fishing",
        zh: "钓鱼"
    },
    "Select Fishing Rod": {
        en: "Select Fishing Rod",
        zh: "选择鱼竿"
    },
    "Fishing...": {
        en: "Fishing...",
        zh: "钓鱼中..."
    },
    "Click when the indicator is in the catch zone!": {
        en: "Click when the indicator is in the catch zone!",
        zh: "当指示器在捕获区域时点击！"
    },
    "Catch!": {
        en: "Catch!",
        zh: "捕获！"
    },
    "You need a fishing rod to fish!": {
        en: "You need a fishing rod to fish!",
        zh: "你需要一个鱼竿来钓鱼！"
    },
    "You caught a": {
        en: "You caught a",
        zh: "你捕获了一个"
    },
    "The fish got away!": {
        en: "The fish got away!",
        zh: "鱼跑掉了！"
    },
    "Basic Fishing Rod": {
        en: "Basic Fishing Rod",
        zh: "基础鱼竿"
    },
    "Improved Fishing Rod": {
        en: "Improved Fishing Rod",
        zh: "改良鱼竿"
    },
    "A simple fishing rod made from basic materials": {
        en: "A simple fishing rod made from basic materials",
        zh: "由基础材料制成的简单鱼竿"
    },
    "A better fishing rod with improved durability and catch rate": {
        en: "A better fishing rod with improved durability and catch rate",
        zh: "一种具有更好耐久性和捕获率的鱼竿"
    },
    ...BIOMES_TRANSLATIONS,
    ...DESCRIPTIONS,
    "Dead Chicken": {
        en: "Dead Chicken",
        zh: "已死亡的鸡"
    },
    "Eat": {
        en: "Eat",
        zh: "吃"
    },
    "Equip": {
        en: "Equip",
        zh: "装备"
    },
    "Craft": {
        en: "Craft",
        zh: "制作"
    },
    "Use": {
        en: "Use",
        zh: "使用"
    },
    // Resource Types
    "resource_type_edible": {
        en: "Edible",
        zh: "食物"
    },
    "resource_type_material": {
        en: "Material",
        zh: "材料"
    },
    "resource_type_tool": {
        en: "Tool",
        zh: "工具"
    },
    "resource_type_valuable": {
        en: "Valuable",
        zh: "贵重物品"
    },
    "resource_type_medicinal": {
        en: "Medicinal",
        zh: "药物"
    },
    "resource_type_harvestable": {
        en: "Harvestable",
        zh: "可收获"
    },
    "resource_type_equipable": {
        en: "Equipable",
        zh: "可装备"
    },
    "resource_type_unknown": {
        en: "Unknown",
        zh: "未知"
    },
    "resource_type_huntable": {
        en: "Huntable",
        zh: "可猎杀"
    },

    // Item names
    "item_small_backpack": {
        en: "Small Backpack",
        zh: "小背包"
    },
    "item_medium_backpack": {
        en: "Medium Backpack",
        zh: "中背包"
    },
    "item_large_backpack": {
        en: "Large Backpack",
        zh: "大背包"
    },
    "item_fish": {
        en: "Fish",
        zh: "鱼"
    },
    "item_seaweed": {
        en: "Seaweed",
        zh: "海藻"
    },
    "item_shellfish": {
        en: "Shellfish",
        zh: "贝类"
    },
    "item_sea_cucumber": {
        en: "Sea Cucumber",
        zh: "海参"
    },
    "item_squid": {
        en: "Squid",
        zh: "鱿鱼"
    },
    "item_salt": {
        en: "Salt",
        zh: "盐"
    },
    "item_sand": {
        en: "Sand",
        zh: "沙子"
    },
    "item_coral": {
        en: "Coral",
        zh: "珊瑚"
    },
    "item_shell": {
        en: "Shell",
        zh: "贝壳"
    },
    "item_pearl": {
        en: "Pearl",
        zh: "珍珠"
    },
    "item_seaweed_fiber": {
        en: "Seaweed Fiber",
        zh: "海藻纤维"
    },
    "item_fish_bone": {
        en: "Fish Bone",
        zh: "鱼骨"
    },
    "item_fish_scales": {
        en: "Fish Scales",
        zh: "鱼鳞"
    },
    "item_fishing_net": {
        en: "Fishing Net",
        zh: "渔网"
    },
    "item_coral_knife": {
        en: "Coral Knife",
        zh: "珊瑚刀"
    },
    "item_healing_algae": {
        en: "Healing Algae",
        zh: "治疗藻类"
    },
    "item_fish_oil": {
        en: "Fish Oil",
        zh: "鱼油"
    },
    "item_coconut": {
        en: "Coconut",
        zh: "椰子"
    },
    "item_beach_plum": {
        en: "Beach Plum",
        zh: "海滩李子"
    },
    "item_sea_grape": {
        en: "Sea Grape",
        zh: "海葡萄"
    },
    "item_beach_almond": {
        en: "Beach Almond",
        zh: "海滩杏仁"
    },
    "item_crab": {
        en: "Crab",
        zh: "螃蟹"
    },
    "item_coconut_shell": {
        en: "Coconut Shell",
        zh: "椰子壳"
    },
    "item_palm_fiber": {
        en: "Palm Fiber",
        zh: "棕榈纤维"
    },
    "item_rock": {
        en: "Rock",
        zh: "岩石"
    },
    "item_salt_deposit": {
        en: "Salt Deposit",
        zh: "盐矿"
    },
    "item_aloe_vera": {
        en: "Aloe Vera",
        zh: "芦荟"
    },
    "item_sea_lavender": {
        en: "Sea Lavender",
        zh: "海薰衣草"
    },
    "item_beach_morning_glory": {
        en: "Beach Morning Glory",
        zh: "海滩牵牛花"
    },
    "item_banana": {
        en: "Banana",
        zh: "香蕉"
    },
    "item_blue_berry": {
        en: "Blue Berry",
        zh: "蓝莓"
    },
    "item_mango": {
        en: "Mango",
        zh: "芒果"
    },
    "item_honey": {
        en: "Honey",
        zh: "蜂蜜"
    },
    "item_red_mushroom": {
        en: "Red Mushroom",
        zh: "红蘑菇"
    },
    "item_wood": {
        en: "Wood",
        zh: "木材"
    },
    "item_bamboo": {
        en: "Bamboo",
        zh: "竹子"
    },
    "item_tree_bark": {
        en: "Tree Bark",
        zh: "树皮"
    },
    "item_plant_fibers": {
        en: "Plant Fibers",
        zh: "植物纤维"
    },
    "item_feathers": {
        en: "Feathers",
        zh: "羽毛"
    },
    "item_tree_sap": {
        en: "Tree Sap",
        zh: "树液"
    },
    "item_wooden_tools": {
        en: "Wooden Tools",
        zh: "木制工具"
    },
    "item_bamboo_tools": {
        en: "Bamboo Tools",
        zh: "竹制工具"
    },
    "item_healing_herbs": {
        en: "Healing Herbs",
        zh: "治疗草药"
    },

    // UI elements
    "ui_inventory": {
        en: "Inventory:",
        zh: "物品栏："
    },
    "ui_inventory_slots": {
        en: "slots",
        zh: "格"
    },
    "ui_no_items": {
        en: "No items in inventory",
        zh: "物品栏中没有物品"
    },
    "ui_quantity": {
        en: "Quantity",
        zh: "数量"
    },
    "ui_consume": {
        en: "Consume",
        zh: "食用"
    },
    "ui_use": {
        en: "Use",
        zh: "使用"
    },
    "ui_gather": {
        en: "Gather",
        zh: "采集"
    },
    "ui_gathering": {
        en: "Gathering",
        zh: "正在采集"
    },
    "ui_available_recipes": {
        en: "Available Recipes",
        zh: "可用配方"
    },
    "ui_recipe_details": {
        en: "Recipe Details",
        zh: "配方详情"
    },
    "ui_select_recipe": {
        en: "Select a recipe to view details",
        zh: "选择一个配方查看详情"
    },
    "ui_ingredients": {
        en: "Ingredients",
        zh: "材料"
    },
    "ui_result": {
        en: "Result",
        zh: "结果"
    },
    "ui_craft": {
        en: "Craft",
        zh: "制作"
    },
    "ui_available": {
        en: "✅ Available",
        zh: "✅ 可制作"
    },
    "ui_missing_ingredients": {
        en: "❌ Missing ingredients",
        zh: "❌ 缺少材料"
    },

    // Weather
    "weather_clear": {
        en: "Clear",
        zh: "晴朗"
    },
    "weather_cloudy": {
        en: "Cloudy",
        zh: "多云"
    },
    "weather_rainy": {
        en: "Rainy",
        zh: "下雨"
    },
    "weather_stormy": {
        en: "Stormy",
        zh: "暴风雨"
    },
    "weather_foggy": {
        en: "Foggy",
        zh: "雾"
    },
    "weather_hot": {
        en: "Heat Wave",
        zh: "热浪"
    },
    "weather_cold": {
        en: "Cold Snap",
        zh: "寒流"
    },

    // Weather descriptions
    "weather_desc_clear": {
        en: "Clear skies and sunshine.",
        zh: "晴朗的天空和阳光。"
    },
    "weather_desc_cloudy": {
        en: "Clouds cover the sky.",
        zh: "云覆盖天空。"
    },
    "weather_desc_rainy": {
        en: "Rain is pouring from the sky.",
        zh: "天空中正在下雨。"
    },
    "weather_desc_stormy": {
        en: "A storm is raging in the sky.",
        zh: "天空中正在发生风暴。"
    },
    "weather_desc_foggy": {
        en: "Fog is blocking the view.",
        zh: "大雾阻碍了视线。"
    },
    "weather_desc_hot": {
        en: "The air is scorching.",
        zh: "空气中正在燃烧。"
    },
    "weather_desc_cold": {
        en: "The air is freezing.",
        zh: "空气中正在结冰。"
    },

    // Settings UI
    "ui_settings": {
        en: "Settings",
        zh: "设置"
    },
    "ui_language": {
        en: "Language",
        zh: "语言"
    },
    "ui_volume": {
        en: "Volume",
        zh: "音量"
    },
    "ui_master_volume": {
        en: "Master Volume",
        zh: "主音量"
    },
    "ui_music_volume": {
        en: "Music Volume",
        zh: "音乐音量"
    },
    "ui_sfx_volume": {
        en: "SFX Volume",
        zh: "音效音量"
    },
    "ui_weather_volume": {
        en: "Weather Volume",
        zh: "天气音量"
    },
    "ui_ui_volume": {
        en: "UI Volume",
        zh: "界面音量"
    },
    "ui_close": {
        en: "Close",
        zh: "关闭"
    },

    // Battle system
    "ui_hunt": {
        en: "Hunt",
        zh: "狩猎"
    },
    "battle_title": {
        en: "Battle",
        zh: "战斗"
    },
    "battle_start": {
        en: "Battle started against",
        zh: "开始与以下敌人战斗："
    },
    "battle_victory": {
        en: "Victory! You defeated the enemy.",
        zh: "胜利！你击败了敌人。"
    },
    "battle_defeat": {
        en: "Defeat! You were defeated.",
        zh: "失败！你被击败了。"
    },
    "battle_flee": {
        en: "You fled from battle.",
        zh: "你逃离了战斗。"
    },
    "battle_flee_fail": {
        en: "Failed to flee! The enemy attacks.",
        zh: "逃跑失败！敌人发动攻击。"
    },
    "battle_health": {
        en: "HP",
        zh: "生命值"
    },
    "battle_ap": {
        en: "AP",
        zh: "行动点"
    },
    "battle_ap_cost": {
        en: "Cost",
        zh: "消耗"
    },
    "battle_damage": {
        en: "damage",
        zh: "伤害"
    },
    "battle_defense": {
        en: "defense",
        zh: "防御"
    },
    "battle_not_enough_ap": {
        en: "Not enough action points!",
        zh: "行动点不足！"
    },
    "battle_action_attack": {
        en: "Attack",
        zh: "攻击"
    },
    "battle_action_defend": {
        en: "Defend",
        zh: "防御"
    },
    "battle_action_heal": {
        en: "Heal",
        zh: "治疗"
    },
    "battle_action_rest": {
        en: "Rest",
        zh: "休息"
    },
    "battle_player_attack": {
        en: "You attack for",
        zh: "你造成了"
    },
    "battle_player_defend": {
        en: "You defend for",
        zh: "你增加了"
    },
    "battle_player_heal": {
        en: "You heal for",
        zh: "你恢复了"
    },
    "battle_player_rest": {
        en: "You rest and recover",
        zh: "你休息并恢复了"
    },
    "battle_enemy_attack": {
        en: "attacks you for",
        zh: "攻击了你，造成"
    },
    "battle_enemy_heal": {
        en: "heals for",
        zh: "恢复了"
    },
    "battle_enemy_rest": {
        en: "rests and recovers AP.",
        zh: "休息并恢复了行动点。"
    },

    // Equipment UI
    "ui_equipment": {
        en: "Equipment",
        zh: "装备"
    },
    "ui_backpack": {
        en: "Backpack",
        zh: "背包"
    },
    "ui_head": {
        en: "Head",
        zh: "头部"
    },
    "ui_body": {
        en: "Body",
        zh: "身体"
    },
    "ui_hands": {
        en: "Hands",
        zh: "手部"
    },
    "ui_feet": {
        en: "Feet",
        zh: "脚部"
    },
    "ui_unequip": {
        en: "Unequip",
        zh: "卸下"
    },
    "Building...": {
        en: "Building...",
        zh: "正在建造..."
    },
    "Working...": {
        en: "Working...",
        zh: "工作中..."
    },
    "SMALL_STORAGE_BOX": {
        en: "Small Storage Box",
        zh: "小型储物箱"
    },
    "SMALL_STORAGE_BOX_desc": {
        en: "A small storage box for keeping items organized.",
        zh: "一个小型储物箱，用于存放物品。"
    },
    "MEDIUM_STORAGE_BOX": {
        en: "Medium Storage Box",
        zh: "中型储物箱"
    },
    "MEDIUM_STORAGE_BOX_desc": {
        en: "A medium storage box for keeping items organized.",
        zh: "一个中型储物箱，用于存放物品。"
    },
    ...WEATHER_TRANSLATIONS,
    ...BOAT_TRANSLATIONS
};
