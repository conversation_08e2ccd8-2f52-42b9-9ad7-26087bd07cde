/* Battle System Styles */

/* Hunt button */
#huntButton {
    background-color: #8b0000;
    color: white;
    border: 1px solid #600;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

#huntButton:hover {
    background-color: #a00;
}

/* Battle container */
#battleContainer {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 727px;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: rgba(0, 0, 0, 0.85);
    border: 2px solid #666;
    border-radius: 10px;
    color: white;
    padding: 15px;
    z-index: 1000;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

/* Pokemon-style battle container */
#battleContainer-pokemon {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

/* Battle header */
#battleHeader {
    text-align: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #666;
    padding-bottom: 10px;
}

#battleHeader h2 {
    margin: 0;
    color: #ff9900;
    font-size: 24px;
}

/* Battle content */
#battleContent {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: stretch;
    margin-bottom: 20px;
    position: relative;
    min-height: 200px;
}

/* Battle stats */
.battle-stats {
    padding: 10px;
    background-color: rgba(50, 50, 50, 0.7);
    border-radius: 5px;
    text-align: left;
    margin: 5px;
    position: relative;
}

/* Enemy stats positioned at top left */
#enemyStats {
    align-self: flex-start;
    width: 60%;
    margin-bottom: 30px;
}

/* Player stats positioned at bottom right */
#playerStats {
    align-self: flex-end;
    width: 60%;
    /* margin-top: 30px; */
}

.entity-icon {
    font-size: 32px;
    margin-bottom: 5px;
}

.entity-name {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 18px;
}

/* Health bar */
.health-bar-container {
    width: 100%;
    height: 15px;
    background-color: #333;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 5px;
}

.health-bar {
    height: 100%;
    background-color: #2ecc71;
    transition: width 0.3s ease-in-out;
}

.enemy-health {
    background-color: #e74c3c;
}

.health-text {
    font-size: 14px;
    margin-bottom: 5px;
}

.ap-container {
    font-size: 14px;
    color: #3498db;
}

/* Battle VS */
#battleVs {
    font-size: 24px;
    font-weight: bold;
    margin: 0 15px;
    color: #ff9900;
}

/* Battle log */
#battleLog {
    background-color: rgba(30, 30, 30, 0.7);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 15px;
    max-height: 120px;
    overflow-y: auto;
    font-size: 14px;
    min-height: 100px;
}

.log-entry {
    margin-bottom: 5px;
    padding: 5px;
    border-bottom: 1px solid #444;
}

.log-entry:last-child {
    border-bottom: none;
    color: #ff9900;
}

/* Battle actions */
#battleActions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
    /* max-height: 100px; */
    /* max-width: 500px; */
}

.battle-action {
    background-color: rgba(50, 50, 50, 0.7);
    border: 1px solid #666;
    border-radius: 5px;
    padding: 10px;
    cursor: pointer;
    /* display: flex; */
    flex-direction: column;
    align-items: center;
    transition: background-color 0.2s;
}

.battle-action:hover:not(.disabled) {
    background-color: rgba(70, 70, 70, 0.7);
}

.battle-action.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.action-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.action-cost {
    font-size: 12px;
    color: #3498db;
}

/* Battle controls */
#battleControls {
    display: flex;
    justify-content: center;
}

#fleeButton {
    background-color: #7f8c8d;
    color: white;
    border: 1px solid #666;
    padding: 8px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#fleeButton:hover {
    background-color: #95a5a6;
}