<?xml version="1.0" ?>

<!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
<svg width="800px" height="800px" viewBox="0 0 128 128" version="1.1" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">

<style type="text/css">
	.st0{fill:#69A401;}
	.st1{fill:#EFE691;}
	.st2{fill:#B20000;}
	.st3{fill:#DF1801;}
	.st4{fill:#F40603;}
	.st5{fill:#FFEEEE;}
	.st6{fill:#847B3C;}
	.st7{fill:#CEB600;}
	.st8{fill:#F8CD02;}
	.st9{fill:#F7C800;}
	.st10{fill:#F6E8B9;}
	.st11{fill:#F6E9CA;}
	.st12{fill:#CF8A11;}
	.st13{fill:#286F0D;}
	.st14{fill:#63271D;}
	.st15{fill:#EB8102;}
	.st16{fill:#E37303;}
	.st17{fill:#D97102;}
	.st18{fill:#BF6302;}
	.st19{fill:#EA9735;}
	.st20{fill:#3E1A01;}
	.st21{fill:#C96A0A;}
	.st22{fill:#CE2335;}
	.st23{fill:#C0242D;}
	.st24{fill:#BA1A23;}
	.st25{fill:#F9DCC7;}
	.st26{fill:#DBE2CE;}
	.st27{fill:#7D4B12;}
	.st28{fill:#75480C;}
	.st29{fill:#66410C;}
	.st30{fill:#88550D;}
	.st31{fill:#FFFEE9;}
	.st32{fill:#9B9F1A;}
	.st33{fill:#F6E177;}
	.st34{fill:#443A00;}
	.st35{fill:#305209;}
	.st36{fill:#7F7C04;}
	.st37{fill:#BAB424;}
	.st38{fill:#F7CF43;}
	.st39{fill:#DE940E;}
	.st40{fill:#5F570A;}
	.st41{fill:#175424;}
	.st42{fill:#215B25;}
	.st43{fill:#1B5020;}
	.st44{fill:#C0F9C0;}
	.st45{fill:#F3DA78;}
	.st46{fill:#BC441C;}
	.st47{fill:#148E2E;}
	.st48{fill:#283767;}
	.st49{fill:#425285;}
	.st50{fill:#CFDFFF;}
	.st51{fill:#1F2C55;}
	.st52{fill:#776220;}
	.st53{fill:#90236B;}
	.st54{fill:#5D1A47;}
	.st55{fill:#99499A;}
	.st56{fill:#FCCAFA;}
	.st57{fill:#917C31;}
	.st58{fill:#F4C435;}
	.st59{fill:#F1BC02;}
	.st60{fill:#F0B102;}
	.st61{fill:#F1F7BA;}
	.st62{fill:#E3DCB9;}
	.st63{fill:#BD6800;}
	.st64{fill:#E19704;}
	.st65{fill:#B2CA2B;}
	.st66{fill:#AFC20F;}
	.st67{fill:#B9CB00;}
	.st68{fill:#E5F392;}
	.st69{fill:#F78202;}
	.st70{fill:#F79613;}
	.st71{fill:#331F07;}
	.st72{fill:#402B16;}
	.st73{fill:#669404;}
	.st74{fill:#F58E13;}
	.st75{fill:#D87117;}
	.st76{fill:#216604;}
	.st77{fill:#286D08;}
	.st78{fill:#C8C625;}
	.st79{fill:#2C441F;}
	.st80{fill:#F1E6BF;}
	.st81{fill:#F2BE2E;}
	.st82{fill:#BF8F33;}
	.st83{fill:#568804;}
	.st84{fill:#669614;}
	.st85{fill:#688E0C;}
	.st86{fill:#4C7005;}
	.st87{fill:#A0CA49;}
	.st88{fill:#99BD70;}
	.st89{fill:#78AA25;}
	.st90{fill:#4B7C23;}
	.st91{fill:#EADBC8;}
	.st92{fill:#F0D5B0;}
	.st93{fill:#DF2B2B;}
	.st94{fill:#D1262C;}
	.st95{fill:#B7252C;}
	.st96{fill:#46670C;}
	.st97{fill:#F49D5B;}
	.st98{fill:#F57A55;}
	.st99{fill:#F1C3A7;}
	.st100{fill:#CC0917;}
	.st101{fill:#DC1035;}
	.st102{fill:#9BAC0F;}
	.st103{fill:#667A1D;}
	.st104{fill:#7A9D18;}
	.st105{fill:#F6F7E6;}
	.st106{fill:#F0194D;}
	.st107{fill:#362420;}
	.st108{fill:#530618;}
	.st109{fill:#44041A;}
	.st110{fill:#490419;}
	.st111{fill:#F8A459;}
	.st112{fill:#871B22;}
	.st113{fill:#600613;}
	.st114{fill:#F8C790;}
	.st115{fill:#447832;}
	.st116{fill:#7C473D;}
	.st117{fill:#441432;}
	.st118{fill:#51163F;}
	.st119{fill:#5B1A41;}
	.st120{fill:#FCEBF9;}
	.st121{fill:#ECE5CE;}
	.st122{fill:#BC3E2C;}
	.st123{fill:#A60F26;}
	.st124{fill:#C61632;}
	.st125{fill:#BD1331;}
	.st126{fill:#F8B772;}
	.st127{fill:#F7DDAC;}
	.st128{fill:#850E11;}
	.st129{fill:#191200;}
	.st130{fill:#553D2D;}
	.st131{fill:#F9E2D2;}
	.st132{fill:#CA8937;}
	.st133{fill:#462D16;}
	.st134{fill:#6D8916;}
	.st135{fill:#96B54E;}
	.st136{fill:#E3E2DE;}
	.st137{fill:#261811;}
	.st138{fill:#525C11;}
	.st139{fill:#14581E;}
	.st140{fill:#3D7712;}
	.st141{fill:#9BC148;}
	.st142{fill:#E22434;}
	.st143{fill:#C6DD9E;}
	.st144{fill:#F89A07;}
	.st145{fill:#F7A410;}
	.st146{fill:#F8AB19;}
	.st147{fill:#F7B81C;}
	.st148{fill:#E5870A;}
	.st149{fill:#97A304;}
	.st150{fill:#A88C5C;}
	.st151{fill:#ADC21E;}
	.st152{fill:#A3BA0B;}
	.st153{fill:#8D9E08;}
	.st154{fill:#E0DAB9;}
	.st155{fill:#684219;}
	.st156{fill:#777F05;}
	.st157{fill:#F2E9C4;}
	.st158{fill:#CBB465;}
	.st159{fill:#FFF5CA;}
	.st160{fill:#E52828;}
	.st161{fill:#F87302;}
	.st162{fill:#FF7B22;}
	.st163{fill:#FC7F10;}
	.st164{fill:#F8A200;}
	.st165{fill:#F8DC91;}
	.st166{fill:#FFFFFF;}
	.st167{fill:#F5D7D5;}
	.st168{fill:#EDA07A;}
	.st169{fill:#FCBEBE;}
	.st170{fill:#EAD991;}
	.st171{fill:#582612;}
</style>

<g id="_x33_0_Mulberry"/>

<g id="_x32_9_Star_Fruit"/>

<g id="_x32_8_Apricot"/>

<g id="_x32_7_Litchi"/>

<g id="_x32_6_Kiwi"/>

<g id="_x32_5_Jackfruit"/>

<g id="_x32_4_Avacado"/>

<g id="_x32_3_Blueberry"/>

<g id="_x32_2_Purple_Grapes"/>

<g id="_x32_1_Melon"/>

<g id="_x32_0_Green_Grapes"/>

<g id="_x31_9_Papaya"/>

<g id="_x31_8_Pineapple"/>

<g id="_x31_7_Banana"/>

<g id="_x31_6_Tender_Coconut"/>

<g id="_x31_5_Strawberry"/>

<g id="_x31_4_Dragon_Fruit"/>

<g id="_x31_3_Plum"/>

<g id="_x31_2_Fig"/>

<g id="_x31_1_Peach"/>

<g id="_x31_0_Cherry"/>

<g id="_x30_9_Sapota"/>

<g id="_x30_8_Custard_Apple"/>

<g id="_x30_7_Watermelon"/>

<g id="_x30_6_Mango"/>

<g id="_x30_5_Pear"/>

<g id="_x30_4_Guava"/>

<g id="_x30_3_Pomegranate"/>

<g id="_x30_2_Orange"/>

<g id="_x30_1_Apple">

<g id="XMLID_795_">

<g id="XMLID_794_">

<path class="st93" d="M84.634,78.852c-5.039,14.218-14.271,28.415-24.831,28.415     c-4.559,0-8.868-1.061-12.701-2.94c-0.793-0.388-1.72-0.388-2.512,0c-3.834,1.879-8.142,2.94-12.701,2.94     C15.934,107.267,3,74.859,3,58.937c0-15.922,12.934-28.829,28.888-28.829c4.56,0,8.868,1.061,12.702,2.94     c0.792,0.388,1.719,0.388,2.511,0c3.834-1.879,8.142-2.94,12.702-2.94c0.448,0,0.894,0.01,1.337,0.03     c12.525,0.57,22.843,10.149,25.255,22.623C87.672,59.362,87.962,68.327,84.634,78.852z" id="XMLID_765_"/>

<path class="st13" d="M47.297,10.465c-3.058,4.796-3.434,10.402-1.551,15.28     c5.454-0.724,10.529-3.669,13.586-8.465S62.766,6.878,60.883,2C55.429,2.724,50.354,5.669,47.297,10.465z" id="XMLID_764_"/>

<path class="st14" d="M46.156,34.288c-1.104,0-2-0.896-2-2v-2.984c0-4.411-2.172-8.481-5.811-10.887     c-0.921-0.609-1.174-1.85-0.565-2.771c0.61-0.921,1.852-1.174,2.771-0.565c4.762,3.149,7.604,8.466,7.604,14.224v2.984     C48.156,33.392,47.261,34.288,46.156,34.288z"/>

<path class="st94" d="M23.504,58.937c0-12.312,7.737-22.816,18.62-26.948c-3.183-1.208-6.629-1.881-10.237-1.881     C15.934,30.107,3,43.015,3,58.937c0,15.922,12.934,48.33,28.888,48.33c4.432,0,8.623-1.009,12.374-2.79     C32.268,96.295,23.504,72.041,23.504,58.937z" id="XMLID_77_"/>

<path class="st95" d="M13.252,58.937c0-14.178,10.259-25.958,23.771-28.369c-1.667-0.3-3.382-0.46-5.135-0.46     C15.934,30.107,3,43.015,3,58.937c0,15.922,12.934,48.33,28.888,48.33c2.15,0,4.241-0.244,6.256-0.69     C24.082,101.857,13.252,73.505,13.252,58.937z" id="XMLID_58_"/>

<ellipse class="st168" cx="29.44" cy="40.603" id="XMLID_53_" rx="2.05" ry="2.067"/>

<ellipse class="st169" cx="20.572" cy="59.007" id="XMLID_54_" rx="2.05" ry="2.067"/>

</g>

<g id="XMLID_793_">

<path class="st159" d="M105.078,125.195c10.97-2.179,19.394-11.499,19.895-22.813     c0.018-0.4,0.027-0.803,0.027-1.208c0-4.119-0.933-8.011-2.585-11.474c-0.129-0.271-0.194-0.561-0.225-0.853H59.955     c-0.031,0.293-0.065,0.6-0.225,0.854c-2.23,3.549-2.23,7.368-2.23,11.487c0,9.539,12.13,17.865,24.634,22.416     C91.39,126.61,99.273,126.348,105.078,125.195z" id="XMLID_798_"/>

<path class="st170" d="M90.724,102.403c7.61,0,13.78-6.069,13.78-13.556h-27.56     C76.944,96.333,83.113,102.403,90.724,102.403z" id="XMLID_801_"/>

<path class="st171" d="M92.144,97.625C92.143,97.625,92.143,97.625,92.144,97.625l-2.841-0.001c-1.104,0-2-0.896-1.999-2.001     c0-1.104,0.896-1.999,2-1.999c0,0,0,0,0.001,0l2.84,0.001c1.104,0,2,0.896,1.999,2.001C94.143,96.73,93.248,97.625,92.144,97.625     z"/>

<path class="st94" d="M105.078,117.961c-5.805,1.153-13.688,1.415-22.945-1.591     c-11.255-4.097-22.386-11.304-24.519-19.651c-0.239,1.435-0.47,2.949-0.47,4.455c0,9.539,12.485,17.878,24.989,22.43     c9.256,3.006,17.139,2.744,22.945,1.591c10.97-2.179,19.394-11.499,19.895-22.813c0.018-0.4,0.027-0.803,0.027-1.208     c0-1.244-0.095-2.465-0.259-3.663C123.26,107.74,115.273,115.936,105.078,117.961z" id="XMLID_59_"/>

</g>

</g>

</g>

</svg>