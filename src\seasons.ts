// Seasons system for the game

export enum Season {
    Spring = 'Spring',
    Summer = 'Summer',
    Fall = 'Fall',
    Winter = 'Winter'
}

// Season data with sunrise and sunset times (in hours, 24-hour format)
export interface SeasonData {
    name: Season;
    sunriseHour: number; // Hour when sun rises
    sunsetHour: number;  // Hour when sun sets
    duskDuration: number; // Duration of dusk in hours
    daysInSeason: number; // Number of days in this season
    color: string; // Color representation for the season
    icon: string;  // Unicode icon for the season
}

// Define season data
export const SEASONS_DATA: SeasonData[] = [
    {
        name: Season.Spring,
        sunriseHour: 5.5, // 5:30 AM
        sunsetHour: 18.5, // 6:30 PM
        duskDuration: 1.5, // 1.5 hours of dusk
        daysInSeason: 30,
        color: '#7CFC00', // Light green
        icon: '🌱'
    },
    {
        name: Season.Summer,
        sunriseHour: 5, // 5:00 AM
        sunsetHour: 20, // 8:00 PM
        duskDuration: 1.5, // 1.5 hours of dusk
        daysInSeason: 30,
        color: '#FFD700', // Gold
        icon: '☀️'
    },
    {
        name: Season.Fall,
        sunriseHour: 6, // 6:00 AM
        sunsetHour: 19, // 7:00 PM
        duskDuration: 3, // 3 hours of dusk (16:00-19:00)
        daysInSeason: 30,
        color: '#FF8C00', // Dark orange
        icon: '🍂'
    },
    {
        name: Season.Winter,
        sunriseHour: 7, // 7:00 AM
        sunsetHour: 16.5, // 4:30 PM
        duskDuration: 1, // 1 hour of dusk
        daysInSeason: 30,
        color: '#87CEEB', // Sky blue
        icon: '❄️'
    }
];

// Total days in a year
export const DAYS_IN_YEAR = SEASONS_DATA.reduce((total, season) => total + season.daysInSeason, 0);

/**
 * Calculate the current season based on the day of the year
 * @param day Current game day
 * @returns The current season data
 */
export function getCurrentSeason(day: number): SeasonData {
    // Normalize day to be within a year cycle
    const dayOfYear = ((day - 1) % DAYS_IN_YEAR) + 1;

    let dayCount = 0;
    for (const season of SEASONS_DATA) {
        dayCount += season.daysInSeason;
        if (dayOfYear <= dayCount) {
            return season;
        }
    }

    // Default to spring if something goes wrong
    return SEASONS_DATA[0];
}

/**
 * Get the day of the current season
 * @param day Current game day
 * @returns The day number within the current season (1-based)
 */
export function getDayOfSeason(day: number): number {
    // Normalize day to be within a year cycle
    const dayOfYear = ((day - 1) % DAYS_IN_YEAR) + 1;

    let dayCount = 0;
    for (const season of SEASONS_DATA) {
        const previousDayCount = dayCount;
        dayCount += season.daysInSeason;
        if (dayOfYear <= dayCount) {
            return dayOfYear - previousDayCount;
        }
    }

    // Default to day 1 if something goes wrong
    return 1;
}

/**
 * Get the year number based on the day
 * @param day Current game day
 * @returns The year number (1-based)
 */
export function getYear(day: number): number {
    return Math.floor((day - 1) / DAYS_IN_YEAR) + 1;
}

/**
 * Determine the time of day based on the hour and current season
 * @param hour Current hour (0-23)
 * @param season Current season data
 * @returns 'day', 'dusk', or 'night'
 */
export function getTimeOfDay(hour: number, season: SeasonData): 'day' | 'dusk' | 'night' {
    // For Fall season (as specified in requirements):
    // - Night: 19:00 - 06:00
    // - Day: 06:00 - 16:00
    // - Dusk: 16:00 - 19:00

    // For other seasons, use the season data
    const duskStart = season.sunsetHour - season.duskDuration;
    const nightStart = season.sunsetHour;
    const nightEnd = season.sunriseHour;

    if (hour >= nightStart || hour < nightEnd) {
        return 'night';
    } else if (hour >= duskStart && hour < nightStart) {
        return 'dusk';
    } else {
        return 'day';
    }
}

/**
 * Get the time bar gradient based on the current season
 * @param season Current season data
 * @returns CSS linear gradient string
 */
export function getTimeBarGradient(season: SeasonData): string {
    // Calculate percentages for the time bar
    const dayStart = (season.sunriseHour / 24) * 100;
    const duskStart = ((season.sunsetHour - season.duskDuration) / 24) * 100;
    const nightStart = (season.sunsetHour / 24) * 100;

    return `linear-gradient(to right,
rgb(101, 0, 201) 0%, rgb(101, 0, 201) ${dayStart}%, /* Purple for night */
        #FF4500 ${dayStart}%, #FF4500 ${duskStart}%, /* Red for day */
        #FFD700 ${duskStart}%, #FFD700 ${nightStart}%, /* Yellow for dusk */
        rgb(101, 0, 201) ${nightStart}%, rgb(101, 0, 201) 100% /* Purple for night */
    )`;
}
