/* Custom styling for Radix UI Tabs */

/* Position the tabs at the bottom of the screen */
.bottom-tabs {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.85);
  background-image:
    linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(to right, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  display: flex;
  justify-content: center;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.5);
}

/* Style the tabs list container */
.bottom-tabs [data-radix-tabs-list] {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  width: 100%;
  max-width: 600px;
  padding: 0.5rem 1rem;
}

/* Style individual tab triggers */
.bottom-tabs [data-radix-tabs-trigger] {
  background: transparent;
  color: rgba(255, 255, 255, 0.6);
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.95rem;
  letter-spacing: 0.02em;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

/* Tab icons */
.bottom-tabs [data-radix-tabs-trigger]::before {
  content: '';
  display: block;
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0.7;
  transition: all 0.25s ease;
  filter: grayscale(30%);
}

/* Icon for each tab */
.bottom-tabs [data-radix-tabs-trigger][value="Explore"]::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolygon points='16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76'%3E%3C/polygon%3E%3C/svg%3E");
}

.bottom-tabs [data-radix-tabs-trigger][value="Character"]::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E");
}

.bottom-tabs [data-radix-tabs-trigger][value="Craft"]::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z'%3E%3C/path%3E%3C/svg%3E");
}

.bottom-tabs [data-radix-tabs-trigger][value="Ground"]::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpolyline points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
}

/* Hover effect for tabs */
.bottom-tabs [data-radix-tabs-trigger]:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.bottom-tabs [data-radix-tabs-trigger]:hover::before {
  opacity: 0.9;
  filter: grayscale(0%);
  transform: scale(1.1);
}

/* Active tab styling */
.bottom-tabs [data-radix-tabs-trigger][data-state="active"] {
  color: white;
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-3px);
}

.bottom-tabs [data-radix-tabs-trigger][data-state="active"]::before {
  opacity: 1;
  filter: grayscale(0%) drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
  transform: scale(1.15);
}

/* Underline effect for active tab */
.bottom-tabs [data-radix-tabs-trigger]::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #4a90e2, #9b59b6);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
}

.bottom-tabs [data-radix-tabs-trigger][data-state="active"]::after {
  width: 70%;
  opacity: 1;
}

/* Subtle glow effect for active tab */
.bottom-tabs [data-radix-tabs-trigger][data-state="active"] {
  box-shadow: 0 0 15px rgba(74, 144, 226, 0.2);
}

/* Adjust window container to make room for bottom tabs */
#window-container {
  height: calc(85% - 80px);
  margin-bottom: 80px;
}

/* Add a subtle pulse animation to the active tab */
@keyframes subtle-pulse {
  0% { box-shadow: 0 0 15px rgba(74, 144, 226, 0.2); }
  50% { box-shadow: 0 0 20px rgba(74, 144, 226, 0.3); }
  100% { box-shadow: 0 0 15px rgba(74, 144, 226, 0.2); }
}

.bottom-tabs [data-radix-tabs-trigger][data-state="active"] {
  animation: subtle-pulse 2s infinite ease-in-out;
}
