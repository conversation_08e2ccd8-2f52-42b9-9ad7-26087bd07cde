// @ts-check

import { getText } from '../i18n.js';
import { ResourceType } from '../Interfaces.js';


/** @import { ResourceType } from './resources.js' */


// export const RARITY = {
//     COMMON: "common",
//     UNCOMMON: "uncommon",
//     RARE: "rare",
//     EPIC: "epic",
//     LEGENDARY: "legendary"
// }

export const RARITY = {
    COMMON: 1,
    UNCOMMON: 2,
    RARE: 3,
    EPIC: 4,
    LEGENDARY: 5
}

export const ResourceTypes: { [key: string]: ResourceType} = {
    TRANSPORT: {
        id: 'Transport',
        get name() { return getText("resource_type_transport"); },
        icon: "🚢",
        get description() { return getText("resource_desc_transport"); }
    },
    EDIBLE: {
        id: 'Edible',
        get name() { return getText("resource_type_edible"); },
        icon: "🍽️",
        get description() { return getText("resource_desc_edible"); },
        get invActionDesc() { return getText("Eat"); }
    },
    MATERIAL: {
        id: 'Material',
        get name() { return getText("resource_type_material"); },
        icon: "🧱",
        get description() { return getText("resource_desc_material"); }
    },
    TOOL: {
        id: 'Tool',
        get name() { return getText("resource_type_tool"); },
        icon: "🛠️",
        get description() { return getText("resource_desc_tool"); }
    },
    VALUABLE: {
        id: 'Valuable',
        get name() { return getText("resource_type_valuable"); },
        icon: "💎",
        get description() { return getText("resource_desc_valuable"); }
    },
    MEDICINAL: {
        id: 'Medicinal',
        get name() { return getText("resource_type_medicinal"); },
        icon: "💊",
        get description() { return getText("resource_desc_medicinal"); },
        get invActionDesc() { return getText("Use"); }
    },
    Harvestable: {
        id: 'Harvestable',
        get name() { return getText("resource_type_harvestable"); },
        icon: "🌱",
        get description() { return getText("resource_desc_harvestable"); }
    },
    Equipable: {
        id: 'Equipable',
        get name() { return getText("resource_type_equipable"); },
        icon: "🛠️",
        get description() { return getText("resource_desc_equipable"); },
        get invActionDesc() { return getText("Equip"); }
    },
    HUNTABLE: {
        id: 'Huntable',
        get name() { return getText("resource_type_huntable"); },
        icon: "🐟",
        get description() { return getText("resource_desc_huntable"); }
    },
    UNKNOWN: {
        id: 'Unknown',
        get name() { return getText("resource_type_unknown"); },
        icon: "❓",
        get description() { return getText("resource_desc_unknown"); }
    }
};

export const statsIconMap = {
    food: '🍖',
    water: '💧',
    energy: '⚡',
    health: '❤️', 
}