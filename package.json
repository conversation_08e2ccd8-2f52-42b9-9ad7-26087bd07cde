{"name": "@redblobgames/mapgen2", "version": "2.0.0", "scripts": {"build": "esbuild --analyze --bundle src/mapgen2.js --define:process.env.NODE_ENV='development' --minify --sourcemap --outfile=build/js/_bundle.js", "watch": "esbuild --loader:.js=jsx --analyze --bundle src/mapgen2.js --minify --sourcemap --outfile=build/js/_bundle.js --watch", "vbuild": "vite build", "vdev": "vite", "vwatch": "vite build --watch", "scss-dev": "sass styles/main.scss build/css/main.css", "scss-build": "sass styles:build/css --style=compressed --no-source-map", "sass": "sass styles/sassEntrance.scss build/css/sassEntrance.css"}, "private": true, "license": "Apache-2.0", "abandonedStuff": {"scss": "sass --watch styles/:build/css --style=expanded --embed-source-map --poll", "@vitejs/plugin-react": "^4.3.4", "rollup-plugin-visualizer": "^5.14.0", "vite": "^6.2.5", "@radix-ui/react-dialog": "^1.1.6", "@tailwindcss/vite": "^4.1.3", "tailwind-merge": "^3.1.0", "tailwindcss": "^4.1.3", "clsx": "^2.1.1", "@radix-ui/themes": "^3.2.1", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "tw-animate-css": "^1.2.5"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@react-spring/web": "^9.7.5", "@redblobgames/prng": "https://github.com/redblobgames/prng", "class-variance-authority": "^0.7.1", "delaunator": "^5", "framer-motion": "^12.9.4", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "poisson-disk-sampling": "^2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "simplex-noise": "^2", "three": "^0.175.0", "url-search-utils": "0.2.0", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "classnames": "^2.5.1", "esbuild": "^0.25.2", "sass": "^1.86.0", "typescript": "^5.8.3"}}