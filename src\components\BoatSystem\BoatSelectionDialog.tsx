import React from 'react';
import { getText } from '../../i18n';
import { Boat } from '../../Interfaces';
import { MacOSModal } from '../WindowManagement/MacOSModal';
import { motion } from 'framer-motion';

interface BoatItemProps {
    boat: Boat;
    onSelect: (boatId: string) => void;
}

const BoatItem: React.FC<BoatItemProps> = ({ boat, onSelect }) => {
    return (
        <motion.div
            className="boat-item"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => onSelect(boat.id)}
        >
            <h3>{getText(boat.name)}</h3>
            <p>{getText(boat.description)}</p>
            <div className="boat-stats">
                <div className="stat">
                    <span>{getText('boat_stat_durability')}:</span>
                    <div className="progress-bar">
                        <div 
                            className="progress" 
                            style={{ width: `${(boat.durability / boat.maxDurability) * 100}%` }}
                        />
                    </div>
                    <span>{boat.durability}/{boat.maxDurability}</span>
                </div>
                <div className="stat">
                    <span>{getText('boat_stat_capacity')}:</span>
                    <span>{boat.weightCap}</span>
                </div>
                <div className="stat">
                    <span>{getText('boat_stat_speed')}:</span>
                    <span>{Math.round(boat.speed * 100)}%</span>
                </div>
                <div className="stat">
                    <span>{getText('boat_stat_waterproof')}:</span>
                    <span>{Math.round(boat.waterproof * 100)}%</span>
                </div>
                <div className="stat">
                    <span>{getText('boat_stat_insulation')}:</span>
                    <span>{Math.round(boat.insulation * 100)}%</span>
                </div>
                <div className="stat">
                    <span>{getText('boat_stat_thermal')}:</span>
                    <span>{Math.round(boat.thermalShield * 100)}%</span>
                </div>
            </div>
        </motion.div>
    );
};

interface BoatSelectionDialogProps {
    isOpen: boolean;
    onClose: () => void;
    boats: Boat[];
    onSelectBoat: (boatId: string) => void;
}

export const BoatSelectionDialog: React.FC<BoatSelectionDialogProps> = ({
    isOpen,
    onClose,
    boats,
    onSelectBoat
}) => {
    console.log("BoatSelectionDialog rendered");

    return (
        <MacOSModal
            title={boats.length === 0 ? getText('No boats found') : getText('select_boat')}
            isOpen={isOpen}
            onClose={onClose}
            initialSize={{ width: 600, height: 500 }}
        >
            {boats.length === 0 &&
                <div className="boat-selection-dialog">
                    <h3>{getText('No boats found')}</h3>
                </div>
            }
            {boats.length > 0 &&
                <div className="boat-selection-content">
                    <div className="boat-list">
                    {boats.map(boat => (
                        <BoatItem
                            key={boat.id}
                            boat={boat}
                            onSelect={onSelectBoat}
                        />
                    ))}
                    </div>
                </div>
            }
        </MacOSModal>
    );
}; 