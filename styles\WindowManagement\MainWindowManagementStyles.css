/* Import all component styles */
@import './MacOSWindow.css';
@import './Dock.css';
@import './MacOSUI.css';
@import './MacOSModal.css';

/* Global styles */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f0f0f0;
  overflow: hidden;
}

/* Hide old UI elements */
.button-container {
  display: none !important;
}

.transparent-window {
  display: none !important;
}

/* Panel styles */
.explore-panel,
.character-panel,
.ground-items-panel {
  padding: 16px;
  height: 100%;
  overflow: auto;
}

h2 {
  margin-top: 0;
  color: #333;
  font-size: 18px;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 768px) {
  .macos-window {
    min-width: 280px;
    min-height: 180px;
  }

  .window-titlebar {
    height: 32px;
  }

  .window-content {
    padding: 12px;
  }
}
