import React from "react";
import { InventoryItemStack } from "src/Interfaces";
import { StorageItemSlot } from "./StorageItemSlot";

interface StorageGridProps {
    storageCapacity: number;
    storageStacks: (InventoryItemStack | null)[];
    activeId: string | null;
}

export const StorageGrid = React.memo(({
    storageCapacity,
    storageStacks,
    activeId
}: StorageGridProps) => {
    return (
        <div className="storage-grid">
            {new Array(storageCapacity).fill(null).map((_, index) => (
                <StorageItemSlot
                    key={index}
                    itemStack={storageStacks[index]}
                    stackIndex={index}
                    isActive={`storage-draggable-${index}` === activeId}
                />
            ))}
        </div>
    );
});
