/* Storage Modal Styles */
.storage-modal {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  padding: 10px;
}

.storage-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.storage-section h3 {
  margin: 0;
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

/* Storage Grid Styles */
.storage-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 4px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

/* Storage Item Slot Styles */
.storage-item-slot {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
}

.storage-item-slot:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.storage-item-slot.selected {
  background: rgba(74, 144, 226, 0.3);
  border-color: #4a90e2;
  box-shadow: 0 0 8px rgba(74, 144, 226, 0.5);
}

.storage-item-slot.dragging {
  opacity: 0.5;
  transform: scale(1.05);
}

.storage-item-slot.drag-over {
  background: rgba(74, 144, 226, 0.2);
  border-color: #4a90e2;
  box-shadow: 0 0 12px rgba(74, 144, 226, 0.6);
}

/* Empty Storage Slot */
.empty-storage-slot {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.3);
  font-size: 12px;
}

.empty-storage-slot::before {
  content: '+';
  font-size: 16px;
  opacity: 0.5;
}

/* Drag Overlay for Storage Items */
.drag-overlay-item {
  pointer-events: none;
  z-index: 999999;
  transform: rotate(5deg);
  opacity: 0.9;
}

.drag-overlay-item .inventory-item {
  background: rgba(74, 144, 226, 0.8);
  border: 2px solid #4a90e2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

/* Storage Modal Responsive Design */
@media (max-width: 768px) {
  .storage-modal {
    gap: 15px;
    padding: 8px;
  }
  
  .storage-grid {
    grid-template-columns: repeat(8, 1fr);
    gap: 3px;
    padding: 8px;
  }
  
  .storage-item-slot {
    width: 35px;
    height: 35px;
  }
}

/* Storage Modal Dark Mode Enhancements */
.dark-mode .storage-modal {
  background: rgba(20, 20, 20, 0.95);
}

.dark-mode .storage-section h3 {
  color: #f0f0f0;
  border-bottom-color: rgba(255, 255, 255, 0.3);
}

.dark-mode .storage-grid {
  background: rgba(10, 10, 10, 0.5);
  border-color: rgba(255, 255, 255, 0.15);
}

.dark-mode .storage-item-slot {
  background: rgba(40, 40, 40, 0.8);
  border-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .storage-item-slot:hover {
  background: rgba(60, 60, 60, 0.9);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Animation for item transfers */
@keyframes itemTransfer {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.storage-item-slot.transferring {
  animation: itemTransfer 0.3s ease-in-out;
}

/* Scrollbar styling for storage grid */
.storage-grid::-webkit-scrollbar {
  width: 6px;
}

.storage-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.storage-grid::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.storage-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
