/* Global styles for macOS-like UI */
:root {
  /* Light mode variables */
  --macos-window-bg: rgba(255, 255, 255, 0.95);
  --macos-titlebar-bg: linear-gradient(to bottom, #f9f9f9, #e8e8e8);
  --macos-border-color: rgba(0, 0, 0, 0.1);
  --macos-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  --macos-red: #ff5f57;
  --macos-yellow: #ffbd2e;
  --macos-green: #28c940;

  /* Dark mode variables */
  --macos-dark-window-bg: rgba(40, 40, 40, 0.95);
  --macos-dark-titlebar-bg: linear-gradient(to bottom, #3a3a3a, #2a2a2a);
  --macos-dark-border-color: rgba(0, 0, 0, 0.3);
  --macos-dark-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  --macos-dark-text: #e0e0e0;
  --macos-dark-accent: #0a84ff;
}

/* Game root container */
.game-root {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  /* background-color: #1e1e1e; */
  color: var(--macos-dark-text);
  pointer-events: none; /* Allow right-clicks to pass through to bottom-overlay */
}

/* Main game area */
#game-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* Window manager container */
.window-manager {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* Allow clicks to pass through to game area */
  z-index: 10;
}

/* Each window has pointer-events: auto to capture clicks */
.macos-window {
  pointer-events: auto;
}

/* Animations */
@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.8);
    opacity: 0;
  }
}

/* Apply animations */
.window-enter {
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.window-exit {
  animation: scaleOut 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dock {
    padding: 4px;
  }

  .dock-item {
    margin: 0 4px;
  }

  .dock-icon {
    width: 36px;
    height: 36px;
  }

  .dock-icon img {
    width: 20px;
    height: 20px;
  }
}
