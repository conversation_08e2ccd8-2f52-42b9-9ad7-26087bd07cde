/* Building System Switcher Styles */

.building-system-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.building-system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 5px 5px 0 0;
}

.building-system-header h3 {
  margin: 0;
  color: #fff;
  font-size: 18px;
}

.view-toggle {
  display: flex;
  gap: 5px;
}

.toggle-button {
  padding: 5px 15px;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.toggle-button.active {
  background-color: #4a6fa5;
  border-color: #4a6fa5;
}

.building-system-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: row;
}