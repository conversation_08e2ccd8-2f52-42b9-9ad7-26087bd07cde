<x:document xmlns:x="http://local/" title="Map icons">

  <x:published>03 Apr 2018</x:published>
  
  <x:section>

    <p>
      For the <a href="./">HTML5 version of mapgen2</a> I added an "icon" feature that adds a hand-drawn icon at the center of each polygon. 
    </p>
    
    <figure>
      <img src="map-icons.png"/>
    </figure>

    <p>
      This is an easy-to-implement technique that can make the maps look hand drawn. Since the polygons are already spaced evenly but not on a grid, we can reuse the polygon locations for these icons, and it looks like they're scattered about.
    </p>

    <p>
      I drew these icons on an iPad using the <a href="https://concepts.tophatch.com/">Concepts app</a> and exported them to SVG. (<a href="map-icons.svg">see exported SVG</a>). Feel free to use these icons in your own projects, <a rel="license" href="https://creativecommons.org/licenses/by/4.0/"><img alt="Creative Commons License" style="border-width:0" src="https://i.creativecommons.org/l/by/4.0/88x31.png" /></a><br />under the <a rel="license" href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution 4.0 International License</a>.
    </p>

    <p>
      I was inspired by <a href="https://www.deviantart.com/starraven/art/Sketchy-Cartography-Brushes-198264358">StarRaven's Sketchy Cartography Brushes</a>, which were designed for use as Photoshop brushes for people drawing maps by hand. StarRaven also has <a href="https://www.cartographersguild.com/showthread.php?t=27940&amp;p=251514#post251514">another set</a> available. I drew my own because I wanted icons that matched the biomes I was using. The biome colors are:
    </p>

    <ul id="biome-colors">
    </ul>
    
  </x:section>

  <x:footer>
    <script type="module">
      import {discreteColors} from "./colormap.js";
      let html = ``;
      for (let [biome, color] of Object.entries(discreteColors)) {
          html += `&lt;li&gt;&lt;span style="display:inline-block;width:4em;background-color:${color}"&gt;&#160;&lt;/span&gt; ${biome} &lt;/li&gt;`;
      }
      document.querySelector("#biome-colors").innerHTML = html;
    </script>
    Created 03 Apr 2018; &#160;
    <!-- hhmts start -->Last modified: 14 Jan 2025<!-- hhmts end -->
  </x:footer>
  
  
</x:document>

