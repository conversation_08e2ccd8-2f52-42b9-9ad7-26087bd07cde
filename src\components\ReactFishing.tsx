import React = require("react");
import { getText } from "src/i18n";
import { Items } from "src/enums/resources";
import { RARITY } from "src/enums/common_enum";
import { terrainInfo } from "src/terrain_info";
import { startProgressBar } from "src/gameinfo";
import { Item } from "src/Interfaces";
import { useRootStore } from "../stores/rootStore";
import { FishingResultModal } from "./FishingModals";
import { ChooseFishingRodModal } from "../MyDialogModal";
import { FISH_BY_BIOME } from "src/enums/fishing_enums";

// Component for the fishing mini-game
const FishingMiniGame: React.FC<{
    selectedRod: Item;
    onCatch: (success: boolean, fish?: Item) => void;
    onCancel: () => void;
}> = ({ selectedRod, onCatch, onCancel }) => {
    // State for the fishing mini-game
    const [position, setPosition] = React.useState(0);
    const [direction, setDirection] = React.useState(1);
    const [catchZone, setCatchZone] = React.useState({ start: 0.3, end: 0.7 });
    const [targetFishRarity, setTargetFishRarity] = React.useState(RARITY.COMMON);
    const [isActive, setIsActive] = React.useState(true);

    // Ref for the animation frame
    const animationRef = React.useRef<number | null>(null);

    // Initialize the fishing mini-game
    React.useEffect(() => {
        // Determine potential fish rarity based on rod
        const rodRarity = selectedRod.rarity || RARITY.COMMON;

        // Pre-determine fish rarity for catch zone calculation
        const rarityRoll = Math.random();
        let fishRarity = RARITY.COMMON;

        // Determine fish rarity based on rod quality and random chance
        if (rodRarity === RARITY.LEGENDARY) {
            if (rarityRoll < 0.1) fishRarity = RARITY.LEGENDARY;
            else if (rarityRoll < 0.3) fishRarity = RARITY.EPIC;
            else if (rarityRoll < 0.5) fishRarity = RARITY.RARE;
            else if (rarityRoll < 0.7) fishRarity = RARITY.UNCOMMON;
            else fishRarity = RARITY.COMMON;
        } else if (rodRarity === RARITY.EPIC) {
            if (rarityRoll < 0.05) fishRarity = RARITY.EPIC;
            else if (rarityRoll < 0.25) fishRarity = RARITY.RARE;
            else if (rarityRoll < 0.5) fishRarity = RARITY.UNCOMMON;
            else fishRarity = RARITY.COMMON;
        } else if (rodRarity === RARITY.RARE) {
            if (rarityRoll < 0.1) fishRarity = RARITY.RARE;
            else if (rarityRoll < 0.4) fishRarity = RARITY.UNCOMMON;
            else fishRarity = RARITY.COMMON;
        } else if (rodRarity === RARITY.UNCOMMON) {
            if (rarityRoll < 0.2) fishRarity = RARITY.UNCOMMON;
            else fishRarity = RARITY.COMMON;
        } else {
            fishRarity = RARITY.COMMON;
        }

        // Store the target fish rarity
        setTargetFishRarity(fishRarity);

        // Set catch zone based on fish rarity (rarer fish are harder to catch)
        let zoneSize: number;
        switch(fishRarity) {
            case RARITY.LEGENDARY:
                zoneSize = 0.15; // Very small catch zone for legendary fish
                break;
            case RARITY.EPIC:
                zoneSize = 0.20; // Small catch zone for epic fish
                break;
            case RARITY.RARE:
                zoneSize = 0.25; // Moderate catch zone for rare fish
                break;
            case RARITY.UNCOMMON:
                zoneSize = 0.30; // Larger catch zone for uncommon fish
                break;
            default:
                zoneSize = 0.35; // Largest catch zone for common fish
        }

        const zoneStart = Math.max(0.1, 0.5 - (zoneSize / 2));
        setCatchZone({
            start: zoneStart,
            end: zoneStart + zoneSize
        });

        // Start the animation
        const animate = () => {
            if (!isActive) return;

            setPosition(prevPosition => {
                let newPosition = prevPosition + 0.01 * direction;

                if (newPosition >= 1) {
                    newPosition = 1;
                    setDirection(-1);
                } else if (newPosition <= 0) {
                    newPosition = 0;
                    setDirection(1);
                }

                return newPosition;
            });

            animationRef.current = requestAnimationFrame(animate);
        };

        animationRef.current = requestAnimationFrame(animate);

        // Clean up animation on unmount
        return () => {
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [selectedRod, direction, isActive]);

    // Handle catch attempt
    const handleCatchAttempt = () => {
        // Stop the animation
        if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
            animationRef.current = null;
        }

        setIsActive(false);

        // Check if catch was successful
        const isInCatchZone = position >= catchZone.start && position <= catchZone.end;

        if (isInCatchZone) {
            // Successful catch
            startProgressBar(getText('Fishing'), 30, () => {
                // Catch a fish based on target rarity
                const fish = catchFish(targetFishRarity, selectedRod);
                if (fish) {
                    onCatch(true, fish);
                }
            });
        } else {
            // Failed catch
            onCatch(false);
        }
    };

    return (
        <div className="fishing-mini-game">
            <div className="fishing-game-header">
                <h3>{getText('Fishing')}</h3>
            </div>
            <div className="fishing-progress-container">
                <div className="fishing-progress-bar">
                    <div
                        className="fishing-catch-zone"
                        style={{
                            left: `${catchZone.start * 100}%`,
                            width: `${(catchZone.end - catchZone.start) * 100}%`
                        }}
                    ></div>
                    <div
                        className="fishing-indicator"
                        style={{ left: `${position * 100}%` }}
                    ></div>
                </div>
            </div>
            <div className="fishing-instructions">
                {getText('Click when the indicator is in the catch zone!')}
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '20px' }}>
                <button
                    className="fishing-catch-button"
                    onClick={handleCatchAttempt}
                    disabled={!isActive}
                >
                    {getText('Catch!')}
                </button>
                <button
                    style={{
                        backgroundColor: '#e74c3c',
                        color: 'white',
                        border: 'none',
                        padding: '10px 20px',
                        borderRadius: '5px',
                        fontSize: '16px',
                        cursor: 'pointer'
                    }}
                    onClick={onCancel}
                >
                    {getText('Cancel')}
                </button>
            </div>
        </div>
    );
};

// Function to catch a fish based on biome, rod rarity, and target fish rarity
function catchFish(targetRarity: string, selectedRod: Item): Item | null {
    const currentBiome = terrainInfo.currentBiome;
    const biomePool = FISH_BY_BIOME[currentBiome] || FISH_BY_BIOME['OCEAN'];

    // Get rod rarity to determine highest possible fish rarity
    const rodRarity = selectedRod.rarity || RARITY.COMMON;

    // Map rod rarity to highest possible fish rarity
    let maxRarity: string;
    switch(rodRarity) {
        case RARITY.LEGENDARY:
            maxRarity = RARITY.LEGENDARY;
            break;
        case RARITY.EPIC:
            maxRarity = RARITY.EPIC;
            break;
        case RARITY.RARE:
            maxRarity = RARITY.RARE;
            break;
        case RARITY.UNCOMMON:
            maxRarity = RARITY.UNCOMMON;
            break;
        default:
            maxRarity = RARITY.COMMON;
    }

    // Filter fish by maximum allowed rarity
    const allowedFish = biomePool.filter(fish => {
        const fishRarity = fish.rarity;

        // Check if fish rarity is allowed based on rod rarity
        if (maxRarity === RARITY.LEGENDARY) return true; // Can catch any fish
        if (maxRarity === RARITY.EPIC) return fishRarity !== RARITY.LEGENDARY;
        if (maxRarity === RARITY.RARE) return fishRarity !== RARITY.LEGENDARY && fishRarity !== RARITY.EPIC;
        if (maxRarity === RARITY.UNCOMMON) return fishRarity === RARITY.UNCOMMON || fishRarity === RARITY.COMMON;
        return fishRarity === RARITY.COMMON; // Common rod can only catch common fish
    });

    // Use the provided target rarity
    const rarityTarget = targetRarity;

    // Filter fish by target rarity
    const possibleFish = allowedFish.filter(fish => fish.rarity === rarityTarget);

    // If no fish of target rarity, fall back to common
    const fishPool = possibleFish.length > 0 ? possibleFish : allowedFish.filter(fish => fish.rarity === RARITY.COMMON);

    // Select random fish from pool
    if (fishPool.length > 0) {
        const selectedFish = fishPool[Math.floor(Math.random() * fishPool.length)];
        return selectedFish;
    }

    // Fallback to generic fish
    return Items['Fish'] || null;
}

// Main fishing modal component
interface FishingModalProps {
    onClose?: () => void;
}

export const FishingModal: React.FC<FishingModalProps> = ({ onClose }) => {
    const { itemStacks, addItemToInventory } = useRootStore();
    const [selectedRod, setSelectedRod] = React.useState<Item | null>(null);
    const [showMiniGame, setShowMiniGame] = React.useState(false);
    const [showResult, setShowResult] = React.useState(false);
    const [resultMessage, setResultMessage] = React.useState('');
    const [resultIcon, setResultIcon] = React.useState('');

    // Get fishing rods from inventory
    const fishingRods = React.useMemo(() => {
        const rods: Item[] = [];

        itemStacks.forEach((stack, index) => {
            if (!stack) return;
            const item = Items[stack.itemId];
            if (item && item.isFishingRod) {
                // Create a new object with the item properties
                const rodWithIndex = { ...item } as any;
                // Add stackIndex as a custom property
                rodWithIndex.stackIndex = index;
                rods.push(rodWithIndex);
            }
        });

        return rods;
    }, [itemStacks]);

    // Handle rod selection
    const handleRodSelect = (rod: Item) => {
        setSelectedRod(rod);
        setShowMiniGame(true);
    };

    // Handle catch result
    const handleCatchResult = (success: boolean, fish?: Item) => {
        setShowMiniGame(false);

        if (success && fish) {
            // Show rarity in the result message for rare and above fish
            let message = getText(`You caught a ${fish.name}!`);
            if (fish.rarity && fish.rarity !== RARITY.COMMON) {
                // Capitalize first letter of rarity
                const rarityName = fish.rarity.charAt(0).toUpperCase() + fish.rarity.slice(1).toLowerCase();
                message = getText(`You caught a ${rarityName} ${fish.name}!`);
            }

            setResultMessage(message);
            setResultIcon(fish.icon);

            // Add fish to inventory
            addItemToInventory(fish);
        } else {
            setResultMessage(getText('The fish got away!'));
            setResultIcon('💨');
        }

        setShowResult(true);
    };

    // Handle cancel
    const handleCancel = () => {
        setShowMiniGame(false);
        setSelectedRod(null);
        if (onClose) onClose();
    };

    // Handle result close
    const handleResultClose = () => {
        setShowResult(false);
        setSelectedRod(null);
        if (onClose) onClose();
    };

    // If no fishing rods, show message
    if (fishingRods.length === 0) {
        return (
            <FishingResultModal
                message={getText('You need a fishing rod to fish!')}
                icon="🎣"
                onClose={() => {
                    if (onClose) onClose();
                }}
            />
        );
    }

    // If only one rod, select it automatically
    React.useEffect(() => {
        if (fishingRods.length === 1 && !selectedRod && !showMiniGame && !showResult) {
            setSelectedRod(fishingRods[0]);
            setShowMiniGame(true);
        }
    }, [fishingRods, selectedRod, showMiniGame, showResult]);

    // Show fishing mini-game if rod is selected
    if (showMiniGame && selectedRod) {
        return (
            <FishingMiniGame
                selectedRod={selectedRod}
                onCatch={handleCatchResult}
                onCancel={handleCancel}
            />
        );
    }

    // Show result modal if there's a result
    if (showResult) {
        return (
            <FishingResultModal
                message={resultMessage}
                icon={resultIcon}
                onClose={handleResultClose}
            />
        );
    }

    // Show rod selection modal if multiple rods
    return (
        <ChooseFishingRodModal
            fishingRods={fishingRods}
            onSelect={handleRodSelect}
        />
    );
}