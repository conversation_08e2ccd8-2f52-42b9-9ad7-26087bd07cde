import React, { useCallback, useState } from 'react';
import { BoatSelectionDialog } from './BoatSelectionDialog';
import { getText } from '../../i18n';
import { useRootStore } from 'src/stores/rootStore';
import { Boat, BoatState } from 'src/Interfaces';
import { Boats } from 'src/enums/boat_enums';
import { MacOSModal } from '../WindowManagement/MacOSModal';
import { Items } from 'src/enums/resources';
import { ResourceTypes } from 'src/enums/common_enum';

interface BoatSystemProps {
    showBoatDialog: boolean;
}

export const BoatSystem: React.FC<BoatSystemProps> = (props: {
    showBoatDialog: boolean
}) => {
    console.log("BoatSystem rendered");

    const [currentBoat, setCurrentBoat] = useState<Boat | null>(null);

    const [showBoatDialog, setShowBoatDialog] = useState(false);

    const { itemStacks, player } = useRootStore();

    if (props.showBoatDialog !== showBoatDialog) {
        setShowBoatDialog(props.showBoatDialog);
    }

    // Get all boats from player's inventory
    const getPlayerBoats = useCallback(() => {
        return itemStacks.filter(stack => Items[stack.itemId] === ResourceTypes.TRANSPORT)
            .map(stack => ({
                ...Boats[stack.itemId],
                id: stack.itemId,
                durability: stack.durability
            }));
    }, [itemStacks]);

    // Select and use a boat
    const selectBoat = useCallback((boatId: string) => {
        const boat = getPlayerBoats().find(b => b.id === boatId);
        if (!boat) return;

        setCurrentBoat(boat);
        window.gameStatus.hasBoat = true;

        // Apply boat effects to player
        player.speed *= boat.speed;
        player.waterResistance = boat.waterproof;
        player.insulation = boat.insulation;
        player.thermalShield = boat.thermalShield;

        // // Start boat durability decay
        // startBoatDurabilityDecay(boat);
    }, [getPlayerBoats, player]);

    // // Exit boat
    // const exitBoat = useCallback(() => {
    //     if (!state.currentBoat) return;

    //     // Remove boat effects from player
    //     player.speed /= state.currentBoat.speed;
    //     player.waterResistance = 0;
    //     player.insulation = 0;
    //     player.thermalShield = 0;

    //     setCurrentBoat(null);
    //     window.gameStatus.hasBoat = false;
    // }, [currentBoat, player]);

    // Damage boat durability over time
    // const startBoatDurabilityDecay = useCallback((boat: Boat) => {
    //     const decayInterval = setInterval(() => {
    //         setState(prev => {
    //             if (!prev.currentBoat) {
    //                 clearInterval(decayInterval);
    //                 return prev;
    //             }

    //             // Damage based on weather and water conditions
    //             let damage = 1;
    //             if (gameStatus.weather === 'stormy') damage *= 2;
    //             if (gameStatus.weather === 'rainy') damage *= 1.5;

    //             const newDurability = prev.currentBoat.durability - damage;

    //             // // Check if boat is broken
    //             // if (newDurability <= 0) {
    //             //     showDialog(getText('boat_broken'));
    //             //     exitBoat();
    //             //     clearInterval(decayInterval);
    //             //     return {
    //             //         ...prev,
    //             //         currentBoat: null,
    //             //         isOnWater: false
    //             //     };
    //             // }

    //             return {
    //                 ...prev,
    //                 currentBoat: {
    //                     ...prev.currentBoat,
    //                     durability: newDurability
    //                 }
    //             };
    //         });
    //     }, 60000); // Check every minute

    //     // Cleanup interval on unmount
    //     return () => clearInterval(decayInterval);
    // }, [gameStatus.weather, exitBoat]);

    // React.useEffect(() => {
    //     setShowBoatDialog(props.showBoatDialog);
    // }, [props.showBoatDialog]);
    
    console.log("showBoatDialog", showBoatDialog);
    const boats = getPlayerBoats();

    return (
        <>
            {/* Boat selection dialog */}
            <BoatSelectionDialog
                isOpen={showBoatDialog}
                onClose={() => setShowBoatDialog(false)}
                boats={getPlayerBoats()}
                onSelectBoat={(boatId) => {
                    selectBoat(boatId);
                    setShowBoatDialog(false);
                }}
            />

            {/* Current boat status */}
            {/* {state.currentBoat && (
                <div className="boat-status">
                    <h3>{getText(state.currentBoat.name)}</h3>
                    <div className="boat-durability">
                        <span>{getText('boat_stat_durability')}:</span>
                        <div className="progress-bar">
                            <div
                                className="progress"
                                style={{
                                    width: `${(state.currentBoat.durability / state.currentBoat.maxDurability) * 100}%`
                                }}
                            />
                        </div>
                        <span>
                            {state.currentBoat.durability}/{state.currentBoat.maxDurability}
                        </span>
                    </div>
                </div>
            )} */}
        </>
    );
}; 