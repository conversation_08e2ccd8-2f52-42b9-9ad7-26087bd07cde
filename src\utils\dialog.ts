import { getText } from '../i18n';

export function showDialog(message: string) {
    const dialog = document.createElement('div');
    dialog.className = 'modal-dialog';
    dialog.innerHTML = `
        <div class="modal-content">
            <p>${message}</p>
            <button class="close-button">${getText('ui_close')}</button>
        </div>
    `;

    document.body.appendChild(dialog);

    dialog.querySelector('.close-button').addEventListener('click', () => {
        document.body.removeChild(dialog);
    });
} 