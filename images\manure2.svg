<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="fcfc0a61-f5fe-413c-a80c-fc7900c94490"  >
</g>
<g transform="matrix(1 0 0 1 540 540)" id="abc24b8f-aed0-4129-8efd-f1e451b6c8d3"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(8.9 0 0 8.9 540 540)"  >
<path style="stroke: rgb(93,60,60); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(93,60,60); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-75, -113.08)" d="M 35.656 135.492 C 28.695 127.235 30.609 112.676 41.664 109.749 C 45.63 123.761 61.838 128.696 76.968 128.696 C 92.091 128.696 103.92 122.848 106.238 111.654 C 116.933 114.091 122.695 125.035 114.645 135.393 C 99.38 155.039 49.508 151.924 35.656 135.492 Z M 76.769 122.779 C 63.98 122.779 47.905 116.783 47.905 106.388 C 47.905 93.332 71.701 92.908 69.594 77.166 C 79.33 78.813 84.058 83.512 84.058 90.939 C 84.058 99.058 77.554 102.047 77.554 102.047 L 80.443 105.987 C 80.443 105.987 88.094 101.146 90.468 99.834 C 96.762 100.688 100.332 104.154 100.332 109.654 C 100.332 117.688 87.387 122.781 76.769 122.779 Z" stroke-linecap="round" />
</g>
</svg>