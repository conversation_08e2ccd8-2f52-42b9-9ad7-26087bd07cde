import React, { forwardRef } from "react";
import { useDraggable, useDroppable } from "@dnd-kit/core";
import { InventoryItemStack } from "src/Interfaces";

// Define the props for the DragDropWrapper
interface DragDropWrapperProps {
  id: string;
  data: any;
  disabled?: boolean;
  // Pass all props to children, not just drag-drop related ones
  children: (props: {
    ref: React.Ref<any>;
    isDragging: boolean;
    isOver: boolean;
    attributes: any;
    listeners: any;
  }) => React.ReactNode;
  // Add any other props that might be passed
  [key: string]: any;
}

// Create a wrapper component that handles drag and drop functionality
export const DragDropWrapper = React.memo(
  forwardRef<any, DragDropWrapperProps>(({ id, data, disabled = false, children, ...otherProps }, _ref) => {
    // Use the dnd-kit hooks
    const { setNodeRef: setDraggableRef, attributes, listeners, isDragging } = useDraggable({
      id: `draggable-${id}`,
      data,
      disabled
    });

    const { setNodeRef: setDroppableRef, isOver } = useDroppable({
      id: `droppable-${id}`,
      data
    });

    // Combine the refs
    const setNodeRef = (node: HTMLElement | null) => {
      setDraggableRef(node);
      setDroppableRef(node);
    };

    // Render the children with the necessary props
    return (
      <>
        {children({
          ref: setNodeRef,
          isDragging,
          isOver,
          attributes,
          listeners,
          // Pass through all other props
          ...otherProps
        })}
      </>
    );
  }),
  // Custom equality function to prevent unnecessary re-renders
  // but allow re-renders when other props change
  (prevProps: DragDropWrapperProps, nextProps: DragDropWrapperProps) => {
    // Check DnD-specific props
    if (prevProps.id !== nextProps.id) return false;
    if (prevProps.disabled !== nextProps.disabled) return false;

    // Check data object
    if (JSON.stringify(prevProps.data) !== JSON.stringify(nextProps.data)) return false;

    // Check all other props by comparing keys and values
    const prevKeys = Object.keys(prevProps).filter(key =>
      !['id', 'data', 'disabled', 'children'].includes(key)
    );

    const nextKeys = Object.keys(nextProps).filter(key =>
      !['id', 'data', 'disabled', 'children'].includes(key)
    );

    // If different number of props, they're not equal
    if (prevKeys.length !== nextKeys.length) return false;

    // Check if any prop value has changed
    for (const key of prevKeys) {
      if (prevProps[key] !== nextProps[key]) return false;
    }

    return true;
  }
);
