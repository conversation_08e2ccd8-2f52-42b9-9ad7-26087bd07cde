/* Boat button */
.boat-button {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    background-color: #2c3e50;
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.boat-button:hover {
    background-color: #34495e;
}

/* Boat selection dialog */
.boat-selection-content {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
}

.boat-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    padding: 16px;
}

.boat-item {
    background-color: #2c3e50;
    border-radius: 8px;
    padding: 16px;
    color: white;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.boat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.boat-item h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
}

.boat-item p {
    margin: 0 0 16px 0;
    font-size: 14px;
    opacity: 0.8;
}

.boat-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.stat {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stat span {
    font-size: 12px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: #27ae60;
    transition: width 0.3s ease;
}

/* Boat status */
.boat-status {
    background-color: #2c3e50;
    border-radius: 8px;
    padding: 16px;
    color: white;
    margin-top: 16px;
}

.boat-status h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
}

.boat-durability {
    display: flex;
    align-items: center;
    gap: 8px;
}

.boat-durability .progress-bar {
    flex: 1;
}

.boat-durability span {
    font-size: 12px;
    white-space: nowrap;
} 