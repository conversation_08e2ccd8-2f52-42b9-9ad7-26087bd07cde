<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cooking System Test</title>
    <link rel="stylesheet" href="../styles/main.css">
    <style>
        body {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background-color: #333;
            color: white;
        }
        
        .test-buttons {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            border: none;
            background-color: #4a90e2;
            color: white;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background-color: #5a9ee2;
        }
    </style>
</head>
<body>
    <h1>Cooking System Test</h1>
    
    <div class="test-buttons">
        <button id="test-fire-light">Test Fire Light Up Modal</button>
        <button id="test-cooking">Test Cooking Modal</button>
    </div>
    
    <div id="test-output">
        <p>Click a button to test the cooking system components.</p>
    </div>
    
    <script type="module">
        import { PlacedBuilding } from '../src/Interfaces.js';
        import { FireLightUpModal } from '../src/components/FireLightUpModal.js';
        import { CookingModal } from '../src/components/CookingModal.js';
        import * as React from 'react';
        import { createRoot } from 'react-dom/client';
        
        // Create test building
        const testBuilding = {
            id: 'test-fire-pit',
            buildingDef: {
                id: 'FIRE_PIT',
                name: 'Fire Pit',
                description: 'A place to cook food and stay warm',
                icon: '🔥',
                resources: [],
                interactions: [],
                width: 1,
                height: 1
            },
            x: 0,
            y: 0,
            isLit: false,
            fireDuration: 0
        };
        
        // Create modal containers
        const fireLightUpModalRoot = document.createElement('div');
        fireLightUpModalRoot.id = 'fire-light-up-modal-root';
        document.body.appendChild(fireLightUpModalRoot);
        
        const cookingModalRoot = document.createElement('div');
        cookingModalRoot.id = 'cooking-modal-root';
        document.body.appendChild(cookingModalRoot);
        
        // Test Fire Light Up Modal
        document.getElementById('test-fire-light').addEventListener('click', () => {
            const modalRoot = createRoot(fireLightUpModalRoot);
            modalRoot.render(
                React.createElement(FireLightUpModal, {
                    building: testBuilding,
                    isOpen: true,
                    onClose: () => {
                        modalRoot.render(
                            React.createElement(FireLightUpModal, {
                                building: testBuilding,
                                isOpen: false,
                                onClose: () => {}
                            })
                        );
                    }
                })
            );
        });
        
        // Test Cooking Modal
        document.getElementById('test-cooking').addEventListener('click', () => {
            // Set fire as lit for cooking test
            testBuilding.isLit = true;
            testBuilding.fireDuration = 60;
            
            const modalRoot = createRoot(cookingModalRoot);
            modalRoot.render(
                React.createElement(CookingModal, {
                    building: testBuilding,
                    isOpen: true,
                    onClose: () => {
                        modalRoot.render(
                            React.createElement(CookingModal, {
                                building: testBuilding,
                                isOpen: false,
                                onClose: () => {}
                            })
                        );
                    }
                })
            );
        });
    </script>
</body>
</html>
