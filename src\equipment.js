// @ts-check

import { addItemToInventory, inventorySystem, updateInventoryDisplay } from './inventory.js';


// Equipment system definition
const equipmentSystem = {
    element: null,
    slots: {
        backpack: null,
        head: null,
        body: null,
        hands: null,
        feet: null
    },
    selectedSlot: null,
    unequipButton: null
};



export function initEquipmentUI() {
    const equipmentWindow = document.getElementById('Equipment');
    if (!equipmentWindow) return;
    
    // Create equipment container
    let html = '<div class="equipment-container">';
    
    // Add person silhouette
    html += '<div class="person-silhouette"><img src="person-silhouette.svg" alt="Character Silhouette"></div>';
    
    html += '<div class="equipment-slots">';
    
    // Backpack slot
    html += createEquipmentSlotHTML('backpack', '🎒', 'Backpack');
    
    // Other equipment slots
    html += createEquipmentSlotHTML('head', '👒', 'Head');
    html += createEquipmentSlotHTML('body', '👕', 'Body');
    html += createEquipmentSlotHTML('hands', '🧤', 'Hands');
    html += createEquipmentSlotHTML('feet', '👞', 'Feet');
    
    html += '</div>';
    html += '</div>';
    
    // Add unequip button
    html += '<button id="unequipButton" style="display: none;">Unequip</button>';
    
    equipmentWindow.innerHTML = html;
    equipmentSystem.element = equipmentWindow;
    
    // Add event listeners to slots
    const slots = equipmentWindow.querySelectorAll('.equipment-slot');
    slots.forEach(slot => {
        slot.addEventListener('click', () => {
            selectEquipmentSlot(slot.dataset.slot);
        });
        
        // Add drop event listeners for drag and drop
        slot.addEventListener('dragover', (e) => {
            e.preventDefault();
            slot.classList.add('drag-over');
        });
        
        slot.addEventListener('dragleave', () => {
            slot.classList.remove('drag-over');
        });
        
        slot.addEventListener('drop', (e) => {
            e.preventDefault();
            slot.classList.remove('drag-over');
            
            // Get the inventory item index
            const itemIndex = e.dataTransfer.getData('text/plain');
            if (itemIndex) {
                const stack = inventorySystem.items[itemIndex];
                if (stack && equipItem(stack.item, slot.dataset.slot)) {
                    // Remove item from inventory if successfully equipped
                    stack.quantity--;
                    if (stack.quantity <= 0) {
                        inventorySystem.items.splice(itemIndex, 1);
                    }
                    
                    // Update inventory display
                    updateInventoryDisplay();
                }
            }
        });
    });
    
    // Add event listener to unequip button
    const unequipButton = document.getElementById('unequipButton');
    if (unequipButton) {
        unequipButton.addEventListener('click', () => {
            if (equipmentSystem.selectedSlot) {
                const item = unequipItem(equipmentSystem.selectedSlot);
                if (item) {
                    window.addItemToInventory(item);
                    // updateInventoryDisplay();
                }
                selectEquipmentSlot(null);
            }
        });
        equipmentSystem.unequipButton = unequipButton;
    }
}


export function equipItem(item, slotType) {
    // Check if item is equipable
    if (item.type.name !== "Equipable") {
        return false;
    }
    
    // Check if slot is valid
    if (!equipmentSystem.slots.hasOwnProperty(slotType)) {
        return false;
    }
    
    // Unequip current item in slot if any
    if (equipmentSystem.slots[slotType]) {
        const currentItem = unequipItem(slotType);
        if (currentItem) {
            // Add current item back to inventory
            window.addItemToInventory(currentItem);
        }
    }
    
    // Equip new item
    equipmentSystem.slots[slotType] = item;
    
    // Apply equipment effects
    applyEquipmentEffects();
    
    // // Update UI
    // updateEquipmentDisplay();
    
    return true;
}

// Unequip an item from a slot
function unequipItem(slotType) {
    // Check if slot is valid and has an item
    if (!equipmentSystem.slots.hasOwnProperty(slotType) || !equipmentSystem.slots[slotType]) {
        return null;
    }
    
    // Get the item
    const item = equipmentSystem.slots[slotType];
    
    // Remove from slot
    equipmentSystem.slots[slotType] = null;
    
    // Remove equipment effects
    applyEquipmentEffects();
    
    // Update UI
    updateEquipmentDisplay();
    
    return item;
}

// Apply effects from all equipped items
function applyEquipmentEffects() {
    // Reset inventory max items to base value
    inventorySystem.maxItems = 20;
    
    // Apply effects from all equipped items
    for (const slotType in equipmentSystem.slots) {
        const item = equipmentSystem.slots[slotType];
        if (item) {
            // Apply effects based on item properties
            if (slotType === 'backpack' && item.capacity) {
                inventorySystem.maxItems += item.capacity;
            }
            
            // Add more effects here as needed
        }
    }
    
    // Update inventory display to show new capacity
    updateInventoryDisplay();
}

// Create HTML for an equipment slot
function createEquipmentSlotHTML(slotId, icon, label) {
    const item = equipmentSystem.slots[slotId];
    const isSelected = equipmentSystem.selectedSlot === slotId;
    const isEmpty = !item;
    
    return `
        <div class="equipment-slot ${isSelected ? 'selected' : ''}" data-slot="${slotId}">
            <div class="slot-icon">${icon}</div>
            <div class="slot-label">${label}</div>
            ${item ? `
                <div class="equipped-item">
                    <div class="item-icon">${item.icon}</div>
                    <div class="item-tooltip">
                        <div class="item-name">${item.name}</div>
                        <div class="item-description">${item.description}</div>
                    </div>
                </div>
            ` : ''}
        </div>
    `;
}

// Select an equipment slot
function selectEquipmentSlot(slotId) {
    equipmentSystem.selectedSlot = slotId;
    
    // Update UI to show selection
    updateEquipmentDisplay();
    
    // Show/hide unequip button
    if (equipmentSystem.unequipButton) {
        if (slotId && equipmentSystem.slots[slotId]) {
            equipmentSystem.unequipButton.style.display = 'block';
        } else {
            equipmentSystem.unequipButton.style.display = 'none';
        }
    }
}

export function updateEquipmentDisplay() {
    if (!equipmentSystem.element) return;
    
    let html = '<div class="equipment-container">';
    
    // Add person silhouette
    html += '<div class="person-silhouette"><img src="person-silhouette.svg" alt="Character Silhouette"></div>';
    
    html += '<div class="equipment-slots">';
    
    // Backpack slot
    html += createEquipmentSlotHTML('backpack', '🎒', 'Backpack');
    
    // Other equipment slots
    html += createEquipmentSlotHTML('head', '👒', 'Head');
    html += createEquipmentSlotHTML('body', '👕', 'Body');
    html += createEquipmentSlotHTML('hands', '🧤', 'Hands');
    html += createEquipmentSlotHTML('feet', '👞', 'Feet');
    
    html += '</div>';
    html += '</div>';
    
    // Add unequip button
    html += '<button id="unequipButton" style="display: ' + 
            (equipmentSystem.selectedSlot && equipmentSystem.slots[equipmentSystem.selectedSlot] ? 'block' : 'none') + 
            '">Unequip</button>';
    
    equipmentSystem.element.innerHTML = html;
    
    // Add event listeners to slots
    const slots = equipmentSystem.element.querySelectorAll('.equipment-slot');
    slots.forEach(slot => {
        slot.addEventListener('click', () => {
            selectEquipmentSlot(slot.dataset.slot);
        });
        
        // Add drop event listeners for drag and drop
        slot.addEventListener('dragover', (e) => {
            e.preventDefault();
            slot.classList.add('drag-over');
        });
        
        slot.addEventListener('dragleave', () => {
            slot.classList.remove('drag-over');
        });
        
        slot.addEventListener('drop', (e) => {
            e.preventDefault();
            slot.classList.remove('drag-over');
            
            // Get the inventory item index
            const itemIndex = e.dataTransfer.getData('text/plain');
            if (itemIndex) {
                const stack = inventorySystem.items[itemIndex];
                if (stack && equipItem(stack.item, slot.dataset.slot)) {
                    // Remove item from inventory if successfully equipped
                    stack.quantity--; 
                }
                // Update inventory display
                updateInventoryDisplay();
            }
        });
    });
}
