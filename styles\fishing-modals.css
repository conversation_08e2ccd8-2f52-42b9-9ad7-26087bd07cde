/* Fishing Modal Styles */

.fishing-rod-selection {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
}

.fishing-rod-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: background-color 0.2s;
}

.fishing-rod-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.rod-icon {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
}

.rod-info {
  flex: 1;
}

.rod-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
}

.rod-quality {
  font-size: 14px;
  color: #ccc;
}

.fishing-result-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  text-align: center;
}

.fishing-result-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.fishing-result-message p {
  font-size: 16px;
  margin-bottom: 20px;
}

/* Building Interactions */
.building-interactions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
}

.interaction-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: rgba(255, 255, 255, 0.05);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.interaction-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Fire Status */
.fire-status {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background-color: rgba(255, 165, 0, 0.2);
  border-radius: 8px;
  margin-bottom: 8px;
}

.fire-icon {
  font-size: 24px;
}

.fire-info {
  flex: 1;
}

.fire-duration {
  font-size: 14px;
  color: #ccc;
  margin-top: 4px;
}
