// Minimap implementation for mapgen2

let minimapCanvas = null;
let minimapCtx = null;
let minimapContainer = null;
let isMinimapVisible = false;
let isFullscreen = true; // Always show in fullscreen mode when visible
let minimapSize = 300; // Default size of the minimap
let originalMinimapSize = 200; // Store original size for toggling back from fullscreen

/**
 * Initialize the minimap
 */
export function initMinimap() {
    // Create minimap container
    minimapContainer = document.createElement('div');
    minimapContainer.id = 'minimap-container';
    minimapContainer.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        width: ${minimapSize}px;
        height: ${minimapSize}px;
        background-color: rgba(0, 0, 0, 0.5);
        border: 2px solid white;
        border-radius: 5px;
        overflow: hidden;
        z-index: 1000;
        display: none;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    `;

    // Create toggle button
    const toggleButton = document.createElement('div');
    toggleButton.id = 'minimap-toggle';
    toggleButton.style.cssText = `
        position: fixed;
        bottom: 50px;
        left: 20px;
        width: 40px;
        height: 40px;
        background-color: rgba(0, 0, 0, 0.7);
        border: 2px solid white;
        border-radius: 5px;
        cursor: pointer;
        z-index: 1001;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        color: white;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    `;
    toggleButton.innerHTML = '🗺️';
    toggleButton.title = 'Show World Map';

    // Create minimap canvas
    minimapCanvas = document.createElement('canvas');
    minimapCanvas.width = minimapSize;
    minimapCanvas.height = minimapSize;
    minimapCtx = minimapCanvas.getContext('2d');

    // Add elements to DOM
    minimapContainer.appendChild(minimapCanvas);
    document.body.appendChild(minimapContainer);
    document.body.appendChild(toggleButton);

    // Add event listener to toggle button
    toggleButton.addEventListener('click', toggleMinimap);
    
    // No need for click event on the map canvas itself anymore
    // as we're always showing in fullscreen mode
}

/**
 * Toggle map visibility - always shows in fullscreen mode
 */
function toggleMinimap() {
    if (isMinimapVisible) {
        // If map is already visible, hide it
        isMinimapVisible = false;
        minimapContainer.style.display = 'none';
        
        // Remove close button
        const closeButton = document.getElementById('minimap-close');
        if (closeButton) {
            closeButton.remove();
        }
    } else {
        // Show map in fullscreen mode immediately
        isMinimapVisible = true;
        isFullscreen = true;
        
        // Save current size before going fullscreen
        originalMinimapSize = minimapSize;
        
        // Calculate fullscreen size (80% of window size)
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        minimapSize = Math.min(windowWidth * 0.99, windowHeight * 0.99);
        
        // Center the map
        minimapContainer.style.width = `${minimapSize}px`;
        minimapContainer.style.height = `${minimapSize}px`;
        minimapContainer.style.left = `${(windowWidth - minimapSize) / 2}px`;
        minimapContainer.style.top = `${(windowHeight - minimapSize) / 2}px`;
        minimapContainer.style.bottom = 'auto';
        minimapContainer.style.zIndex = '2000';
        minimapContainer.style.display = 'block';
        
        // Update canvas size
        minimapCanvas.width = minimapSize;
        minimapCanvas.height = minimapSize;
        
        // Add close button
        if (!document.getElementById('minimap-close')) {
            const closeButton = document.createElement('div');
            closeButton.id = 'minimap-close';
            closeButton.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                width: 30px;
                height: 30px;
                background-color: rgba(0, 0, 0, 0.7);
                border: 2px solid white;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16px;
                color: white;
                z-index: 2001;
            `;
            closeButton.innerHTML = '✖';
            closeButton.title = 'Close Map';
            closeButton.addEventListener('click', (e) => {
                e.stopPropagation();
                isMinimapVisible = false;
                minimapContainer.style.display = 'none';
                closeButton.remove();
            });
            minimapContainer.appendChild(closeButton);
        }
    }
    
    // Update the map
    updateMinimap();
}

/**
 * Exit fullscreen mode
 */
function exitFullscreen() {
    isFullscreen = false;
    minimapSize = originalMinimapSize;
    
    // Reset minimap position and size
    minimapContainer.style.width = `${minimapSize}px`;
    minimapContainer.style.height = `${minimapSize}px`;
    minimapContainer.style.left = '20px';
    minimapContainer.style.top = 'auto';
    minimapContainer.style.bottom = '20px';
    minimapContainer.style.zIndex = '1000';
    
    // Update canvas size
    minimapCanvas.width = minimapSize;
    minimapCanvas.height = minimapSize;
    
    // Remove close button
    const closeButton = document.getElementById('minimap-close');
    if (closeButton) {
        closeButton.remove();
    }
    
    // Update the minimap
    updateMinimap();
}

/**
 * Update the minimap with the current world state
 * @param {Object} world - The world object containing width and height
 * @param {HTMLCanvasElement} bgCanvas - The background canvas with the full map
 * @param {Object} player - The player object with x and y coordinates
 * @param {Object} camera - The camera object with x and y coordinates
 */
export function updateMinimap(world, bgCanvas, player, camera) {
    if (!isMinimapVisible || !minimapCtx) return;

    // Clear the minimap
    minimapCtx.clearRect(0, 0, minimapSize, minimapSize);

    // Calculate scale factor to fit the entire world in the minimap
    const scaleX = minimapSize / world.width;
    const scaleY = minimapSize / world.height;

    // Draw the world map (scaled down)
    minimapCtx.drawImage(bgCanvas, 0, 0, world.width, world.height, 0, 0, minimapSize, minimapSize);

    // Draw the current view area (camera viewport) - only in normal mode or with reduced opacity in fullscreen
    const viewportWidth = camera.width * scaleX;
    const viewportHeight = camera.height * scaleY;
    const viewportX = camera.x * scaleX;
    const viewportY = camera.y * scaleY;

    // In fullscreen mode, make the viewport rectangle more subtle
    if (isFullscreen) {
        minimapCtx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
        minimapCtx.lineWidth = 1;
    } else {
        minimapCtx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        minimapCtx.lineWidth = 2;
    }
    minimapCtx.strokeRect(viewportX, viewportY, viewportWidth, viewportHeight);

    // Draw the player position
    const playerX = player.x * scaleX;
    const playerY = player.y * scaleY;
    
    // Make player marker size relative to the minimap size
    const playerMarkerSize = isFullscreen ? 5 : 3;
    
    minimapCtx.fillStyle = 'red';
    minimapCtx.beginPath();
    minimapCtx.arc(playerX, playerY, playerMarkerSize, 0, Math.PI * 2);
    minimapCtx.fill();
    
    // Add title in fullscreen mode
    if (isFullscreen) {
        minimapCtx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        minimapCtx.font = '16px Arial';
        minimapCtx.textAlign = 'center';
        minimapCtx.fillText('World Map', minimapSize / 2, 25);
    }
}