import React = require("react");
import { FishingModal } from "./ReactFishing";
import { getText } from "src/i18n";

export const FishingButton = () => {
    const [showFishingDialog, setShowFishingDialog] = React.useState(false);

    const handleOpenFishing = () => {
        setShowFishingDialog(true);
    };

    const handleCloseFishing = () => {
        setShowFishingDialog(false);
    };

    return (
        <>
            <button
                id="fishingButton"
                onClick={handleOpenFishing}
            >
                {getText("Fishing")}
            </button>

            {showFishingDialog && (
                <FishingModal onClose={handleCloseFishing} />
            )}
        </>
    );
}