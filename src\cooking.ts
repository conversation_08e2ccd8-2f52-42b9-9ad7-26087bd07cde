import { PlacedBuilding } from './Interfaces';
import { createRoot } from 'react-dom/client';
import * as React from 'react';
import { FireLightUpModal } from './components/FireLightUpModal';
import { CookingModal } from './components/CookingModal';

// Export cooking system
export const cookingSystem = {
  currentBuilding: null as PlacedBuilding | null,
  fireLightUpModalInstance: null as any,
  cookingModalInstance: null as any,
};

// Create a container for modals if it doesn't exist
function ensureModalContainer(id: string): HTMLElement {
  let container = document.getElementById(id);
  if (!container) {
    container = document.createElement('div');
    container.id = id;
    document.body.appendChild(container);
    console.log(`Created new modal container: ${id}`);
  }
  return container;
}

// Open fire light up interface
export function openFireLightUpInterface(building: PlacedBuilding) {
  console.log('Opening fire light up interface for building:', building.id);

  // Clean up any existing instance
  if (cookingSystem.fireLightUpModalInstance) {
    console.log('Unmounting previous fire light up modal');
    cookingSystem.fireLightUpModalInstance.unmount();
    cookingSystem.fireLightUpModalInstance = null;
  }

  cookingSystem.currentBuilding = building;

  // Create a unique container for this modal
  const containerId = `fire-light-up-modal-${building.id}`;
  const container = ensureModalContainer(containerId);

  // Create a new root and render the modal
  const modalRoot = createRoot(container);
  cookingSystem.fireLightUpModalInstance = modalRoot;

  modalRoot.render(
    React.createElement(FireLightUpModal, {
      building,
      isOpen: true,
      portalId: `fire-light-up-portal-${building.id}`,
      onClose: () => {
        console.log('Closing fire light up modal');
        // Update the building reference before closing the modal
        cookingSystem.currentBuilding = null;

        // Unmount the modal completely after a short delay
        setTimeout(() => {
          if (cookingSystem.fireLightUpModalInstance) {
            cookingSystem.fireLightUpModalInstance.unmount();
            cookingSystem.fireLightUpModalInstance = null;

            // Remove the container
            if (container.parentNode) {
              container.parentNode.removeChild(container);
            }
          }
        }, 300);
      }
    })
  );
}

// Open cooking interface
export function openCookingInterface(building: PlacedBuilding) {
  console.log('Opening cooking interface for building:', building.id);

  // Check if fire is lit
  if (!building.isLit) {
    console.log('Fire is not lit');
    return;
  }

  // Clean up any existing instance
  if (cookingSystem.cookingModalInstance) {
    console.log('Unmounting previous cooking modal');
    cookingSystem.cookingModalInstance.unmount();
    cookingSystem.cookingModalInstance = null;
  }

  cookingSystem.currentBuilding = building;

  // Create a unique container for this modal
  const containerId = `cooking-modal-${building.id}`;
  const container = ensureModalContainer(containerId);

  // Create a new root and render the modal
  const modalRoot = createRoot(container);
  cookingSystem.cookingModalInstance = modalRoot;

  modalRoot.render(
    React.createElement(CookingModal, {
      building,
      isOpen: true,
      portalId: `cooking-portal-${building.id}`,
      onClose: () => {
        console.log('Closing cooking modal');
        // Update the building reference before closing the modal
        cookingSystem.currentBuilding = null;

        // Unmount the modal completely after a short delay
        setTimeout(() => {
          if (cookingSystem.cookingModalInstance) {
            cookingSystem.cookingModalInstance.unmount();
            cookingSystem.cookingModalInstance = null;

            // Remove the container
            if (container.parentNode) {
              container.parentNode.removeChild(container);
            }
          }
        }, 300);
      }
    })
  );
}
