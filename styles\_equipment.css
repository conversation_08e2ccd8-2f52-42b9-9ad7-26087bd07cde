
.equipment-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 15px;
    min-height: 350px;
}

.person-silhouette {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    opacity: 0.7;
    
    img {
        width: 200px;
        height: 300px;
    }
}

.equipment-slots {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.equipment-slot {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    padding: 15px;
    width: 80px;
    height: 80px;
    cursor: pointer;
    transition: all 0.2s;
}

/* Position equipment slots around the silhouette */
.equipment-slot[data-slot="head"] {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.equipment-slot[data-slot="body"] {
    top: 40%;
    left: 50%;
    transform: translateX(-50%);
}

.equipment-slot[data-slot="hands"] {
    top: 40%;
    left: calc(50% - 120px);
}

.equipment-slot[data-slot="feet"] {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.equipment-slot[data-slot="backpack"] {
    top: 40%;
    right: calc(50% - 120px);
}

/* Gray styling for empty slots */
.equipment-slot:not(:has(.equipped-item)) .slot-icon {
    filter: grayscale(100%);
    opacity: 0.6;
}

.equipment-slot:hover {
    background: rgba(255, 255, 255, 0.1);
}

.equipment-slot.selected {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.equipment-slot.drag-over {
    background: rgba(100, 255, 100, 0.2);
    box-shadow: 0 0 10px rgba(100, 255, 100, 0.5);
}

.slot-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.slot-label {
    font-size: 14px;
    color: #ccc;
}

.equipped-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 5px;
}

.item-icon {
    font-size: 32px;
    margin-bottom: 5px;
}

.item-tooltip {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    padding: 10px;
    z-index: 100;
    display: none;
    pointer-events: none;
}

.equipped-item:hover .item-tooltip {
    display: block;
}

.item-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.item-description {
    font-size: 12px;
    color: #aaa;
}

#unequipButton {
    display: block;
    width: 100%;
    margin-top: 20px;
    background: rgba(100, 0, 0, 0.7);
    color: white;
    border: 1px solid #666;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
}

#unequipButton:hover {
    background: rgba(150, 0, 0, 0.7);
}
