import React, { forwardRef } from "react";
import { useDraggable, useDroppable } from "@dnd-kit/core";

// Define the props for the StorageDragDropWrapper
interface StorageDragDropWrapperProps {
  id: string;
  data: any;
  disabled?: boolean;
  children: (props: {
    ref: React.Ref<any>;
    isDragging: boolean;
    isOver: boolean;
    attributes: any;
    listeners: any;
    isSelected: boolean;
  }) => React.ReactNode;
  [key: string]: any;
}

// Create a wrapper component that handles drag and drop functionality for storage
export const StorageDragDropWrapper = React.memo(
  forwardRef<any, StorageDragDropWrapperProps>(({ id, data, disabled = false, children, ...otherProps }, _ref) => {
    // Use the dnd-kit hooks with storage-specific IDs
    const { setNodeRef: setDraggableRef, attributes, listeners, isDragging } = useDraggable({
      id: `storage-draggable-${id}`,
      data,
      disabled
    });

    const { setNodeRef: setDroppableRef, isOver } = useDroppable({
      id: `storage-droppable-${id}`,
      data
    });

    // Combine the refs
    const setNodeRef = (node: HTMLElement | null) => {
      setDraggableRef(node);
      setDroppableRef(node);
    };

    // Pass all props to children, including the isSelected from otherProps
    return children({
      ref: setNodeRef,
      isDragging,
      isOver,
      attributes,
      listeners,
      isSelected: otherProps.isSelected || false
    });
  })
);
