// Battle system for mapgen2
import { getText } from './i18n.js';
import { playUISound, playSFX } from './settings.js';
import { terrainInfo } from './terrain_info.js';

// Battle state management
export const battleSystem = {
    inBattle: false,
    currentTurn: 'player', // 'player' or 'enemy'
    player: null,
    enemy: null,
    actions: [],
    battleLog: [],
    maxLogEntries: 5,
    element: null,
    huntButton: null,
    onBattleEnd: null,
    
    // Initialize the battle system
    init(container) {
        console.log('Initializing battle system with container:', container);
        // Create battle UI container
        this.element = document.createElement('div');
        this.element.id = 'battleContainer';
        this.element.className = 'transparent-window';
        this.element.style.display = 'none';
        
        // Add Pokemon-style container
        const pokemonContainer = document.createElement('div');
        pokemonContainer.id = 'battleContainer-pokemon';
        this.element.appendChild(pokemonContainer);
        
        // Use the hunt button that's already in the environment tab
        this.huntButton = document.getElementById('huntButton');
        if (this.huntButton) {
            console.log('Hunt button found, adding event listener');
            this.huntButton.addEventListener('click', () => this.startHunt());
        } else {
            console.error('Hunt button not found in environment tab');
        }
        
        // Add battle container to the main container
        container.appendChild(this.element);
        console.log('Battle container appended to DOM, element ID:', this.element.id);
        
        // Initialize battle UI
        this.updateBattleUI();
    },
    
    // Start hunting for enemies
    startHunt() {
        // playUISound('click');
        
        // Check if player is already in battle
        if (this.inBattle) {
            return;
        }
        console.log('Hunting for enemies...');
        
        // Generate a random enemy based on current biome
        const enemy = this.generateEnemy();
        
        // Start battle with the generated enemy
        this.startBattle(enemy);
    },
    
    // Generate a random enemy based on current biome
    generateEnemy() {
        // Get current biome from terrain info
        const biome = terrainInfo?.currentBiome || 'OCEAN';
        
        // Define enemies for different biomes
        const biomeEnemies = {
            OCEAN: [
                { name: 'Shark', health: 80, maxHealth: 80, ap: 3, maxAp: 3, damage: 15, defense: 5, icon: '🦈' },
                { name: 'Giant Squid', health: 60, maxHealth: 60, ap: 4, maxAp: 4, damage: 12, defense: 3, icon: '🦑' },
                { name: 'Pirate', health: 70, maxHealth: 70, ap: 3, maxAp: 3, damage: 10, defense: 4, icon: '🏴‍☠️' }
            ],
            BEACH: [
                { name: 'Crab Swarm', health: 50, maxHealth: 50, ap: 5, maxAp: 5, damage: 8, defense: 2, icon: '🦀' },
                { name: 'Beach Bandit', health: 65, maxHealth: 65, ap: 3, maxAp: 3, damage: 12, defense: 3, icon: '👤' },
                { name: 'Seagull Flock', health: 40, maxHealth: 40, ap: 4, maxAp: 4, damage: 6, defense: 1, icon: '🐦' }
            ],
            TROPICAL_RAIN_FOREST: [
                { name: 'Jaguar', health: 75, maxHealth: 75, ap: 4, maxAp: 4, damage: 14, defense: 4, icon: '🐆' },
                { name: 'Venomous Snake', health: 45, maxHealth: 45, ap: 3, maxAp: 3, damage: 18, defense: 2, icon: '🐍' },
                { name: 'Wild Boar', health: 85, maxHealth: 85, ap: 2, maxAp: 2, damage: 10, defense: 6, icon: '🐗' }
            ],
            // Add more biomes as needed
        };
        
        // Get enemies for current biome or default to OCEAN
        const enemies = biomeEnemies[biome] || biomeEnemies.OCEAN;
        
        // Select a random enemy from the list
        return enemies[Math.floor(Math.random() * enemies.length)];
    },
    
    // Start a battle with an enemy
    startBattle(enemy) {
        console.log('Starting battle with enemy:', enemy);
        // Set up player stats for battle
        this.player = {
            name: 'Player',
            health: window.gameStatus?.health || 100, // Use default 100 if gameStatus is undefined
            maxHealth: 100,
            ap: 5, // Action points
            maxAp: 5,
            icon: '👤'
        };
        
        // Set enemy
        this.enemy = enemy;
        
        // Set battle state
        this.inBattle = true;
        this.currentTurn = 'player';
        this.battleLog = [];
        
        // Define available actions
        this.actions = [
            { name: getText('battle_action_attack'), apCost: 2, effect: 'damage', value: 10, icon: '⚔️' },
            { name: getText('battle_action_defend'), apCost: 1, effect: 'defense', value: 5, icon: '🛡️' },
            { name: getText('battle_action_heal'), apCost: 3, effect: 'heal', value: 15, icon: '💊' },
            { name: getText('battle_action_rest'), apCost: 0, effect: 'restore_ap', value: 2, icon: '💤' }
        ];
        
        // Show battle UI
        this.element.style.display = 'flex';
        console.log('Set battle UI display to flex, checking styles:', 
                  'display:', this.element.style.display, 
                  'z-index:', getComputedStyle(this.element).zIndex,
                  'position:', getComputedStyle(this.element).position);
        
        // Force the browser to recognize the element
        document.body.offsetHeight;
        
        // Update battle UI
        this.updateBattleUI();
        
        // Add battle start message
        this.addLogMessage(`${getText('battle_start')} ${enemy.name}!`);
        
        // Play battle start sound
        // playSFX('battle_start');
    },
    
    // End the current battle
    endBattle(result) {
        // Add battle end message
        if (result === 'win') {
            this.addLogMessage(getText('battle_victory'));
            playSFX('victory');
        } else if (result === 'lose') {
            this.addLogMessage(getText('battle_defeat'));
            playSFX('defeat');
        } else {
            this.addLogMessage(getText('battle_flee'));
            playSFX('flee');
        }
        
        // Update player health in game status
        if (window.gameStatus) {
            window.gameStatus.health = this.player.health;
            if (window.updateGameStatusUI) {
                window.updateGameStatusUI();
            }
        }
        
        // Reset battle state
        setTimeout(() => {
            this.inBattle = false;
            this.player = null;
            this.enemy = null;
            this.element.style.display = 'none';
            
            // Call onBattleEnd callback if provided
            if (typeof this.onBattleEnd === 'function') {
                this.onBattleEnd(result);
            }
        }, 2000);
    },
    
    // Perform a player action
    performPlayerAction(actionIndex) {
        // Check if it's player's turn
        if (this.currentTurn !== 'player' || !this.inBattle) {
            return;
        }
        
        const action = this.actions[actionIndex];
        
        // Check if player has enough AP
        if (this.player.ap < action.apCost) {
            this.addLogMessage(getText('battle_not_enough_ap'));
            return;
        }
        
        // Deduct AP cost
        this.player.ap -= action.apCost;
        
        // Apply action effect
        let message = '';
        
        switch (action.effect) {
            case 'damage':
                // Calculate damage considering enemy defense
                const damage = Math.max(1, action.value - this.enemy.defense);
                this.enemy.health = Math.max(0, this.enemy.health - damage);
                message = `${getText('battle_player_attack')} ${damage} ${getText('battle_damage')}`;
                playSFX('attack');
                break;
                
            case 'defense':
                // Increase player defense temporarily
                if (!this.player.defense) this.player.defense = 0;
                this.player.defense += action.value;
                message = `${getText('battle_player_defend')} +${action.value} ${getText('battle_defense')}`;
                playSFX('defend');
                break;
                
            case 'heal':
                // Heal player
                this.player.health = Math.min(this.player.maxHealth, this.player.health + action.value);
                message = `${getText('battle_player_heal')} ${action.value} ${getText('battle_health')}`;
                playSFX('heal');
                break;
                
            case 'restore_ap':
                // Restore AP
                this.player.ap = Math.min(this.player.maxAp, this.player.ap + action.value);
                message = `${getText('battle_player_rest')} +${action.value} ${getText('battle_ap')}`;
                playSFX('rest');
                break;
        }
        
        // Add action message to log
        this.addLogMessage(message);
        
        // Update battle UI
        this.updateBattleUI();
        
        // Check if enemy is defeated
        if (this.enemy.health <= 0) {
            this.endBattle('win');
            return;
        }
        
        // Switch to enemy turn
        this.currentTurn = 'enemy';
        
        // Perform enemy action after a short delay
        setTimeout(() => this.performEnemyAction(), 1000);
    },
    
    // Perform enemy action
    performEnemyAction() {
        // Check if it's enemy's turn
        if (this.currentTurn !== 'enemy' || !this.inBattle) {
            return;
        }
        
        // Restore some AP for enemy
        this.enemy.ap = Math.min(this.enemy.maxAp, this.enemy.ap + 1);
        
        // Enemy AI - simple version
        let message = '';
        
        // If enemy health is low, chance to heal
        if (this.enemy.health < this.enemy.maxHealth * 0.3 && Math.random() < 0.4 && this.enemy.ap >= 2) {
            const healAmount = Math.floor(this.enemy.maxHealth * 0.2);
            this.enemy.health = Math.min(this.enemy.maxHealth, this.enemy.health + healAmount);
            this.enemy.ap -= 2;
            message = `${this.enemy.name} ${getText('battle_enemy_heal')} ${healAmount} ${getText('battle_health')}`;
            playSFX('heal');
        }
        // If enemy AP is low, rest
        else if (this.enemy.ap < 2) {
            this.enemy.ap += 2;
            message = `${this.enemy.name} ${getText('battle_enemy_rest')}`;
            playSFX('rest');
        }
        // Otherwise attack
        else {
            // Calculate damage considering player defense
            const damage = Math.max(1, this.enemy.damage - (this.player.defense || 0));
            this.player.health = Math.max(0, this.player.health - damage);
            this.enemy.ap -= 2;
            message = `${this.enemy.name} ${getText('battle_enemy_attack')} ${damage} ${getText('battle_damage')}`;
            playSFX('enemy_attack');
            
            // Reset player defense after enemy attack
            this.player.defense = 0;
        }
        
        // Add action message to log
        this.addLogMessage(message);
        
        // Update battle UI
        this.updateBattleUI();
        
        // Check if player is defeated
        if (this.player.health <= 0) {
            this.endBattle('lose');
            return;
        }
        
        // Restore some AP for player at end of turn
        this.player.ap = Math.min(this.player.maxAp, this.player.ap + 2);
        
        // Switch back to player turn
        this.currentTurn = 'player';
    },
    
    // Add a message to the battle log
    addLogMessage(message) {
        this.battleLog.push(message);
        
        // Keep log at max length
        if (this.battleLog.length > this.maxLogEntries) {
            this.battleLog.shift();
        }
        
        // Update battle UI
        this.updateBattleUI();
    },
    
    // Update the battle UI
    updateBattleUI() {
        console.log('Updating battle UI, element exists:', !!this.element, 'inBattle:', this.inBattle);
        if (!this.element || !this.inBattle) return;
        
        // Get the Pokemon-style container
        const pokemonContainer = this.element.querySelector('#battleContainer-pokemon');
        if (!pokemonContainer) {
            console.error('Pokemon-style container not found');
            return;
        }
        
        // Create battle UI HTML for Pokemon-style battle
        let html = `
            <div id="battleHeader">
                <h2>${getText('battle_title')}</h2>
            </div>
            
            <div id="battleContent">
                <div id="enemyStats" class="battle-stats">
                    <div class="entity-icon">${this.enemy.icon}</div>
                    <div class="entity-name">${this.enemy.name}</div>
                    <div class="health-bar-container">
                        <div class="health-bar enemy-health" style="width: ${(this.enemy.health / this.enemy.maxHealth) * 100}%"></div>
                    </div>
                    <div class="health-text">${getText('battle_health')}: ${this.enemy.health}/${this.enemy.maxHealth}</div>
                </div>
            
                <div id="battleLog">
                    ${this.battleLog.map(msg => `<div class="log-entry">${msg}</div>`).join('')}
                </div>
                
                <div id="playerStats" class="battle-stats">
                    <div class="entity-icon">${this.player.icon}</div>
                    <div class="entity-name">${this.player.name}</div>
                    <div class="health-bar-container">
                        <div class="health-bar" style="width: ${(this.player.health / this.player.maxHealth) * 100}%"></div>
                    </div>
                    <div class="health-text">${getText('battle_health')}: ${this.player.health}/${this.player.maxHealth}</div>
                    <div class="ap-container">
                        ${getText('battle_ap')}: ${this.player.ap}/${this.player.maxAp}
                    </div>
                </div>
            </div>
            
            <div id="battleActions">
                ${this.actions.map((action, index) => `
                    <button class="battle-action ${this.player.ap < action.apCost ? 'disabled' : ''}" 
                            data-index="${index}" 
                            ${this.player.ap < action.apCost ? 'disabled' : ''}>
                        <span class="action-icon">${action.icon}</span>
                        <span class="action-name">${action.name}</span>
                        <span class="action-cost">${getText('battle_ap_cost')}: ${action.apCost}</span>
                    </button>
                `).join('')}
            </div>
            
            <div id="battleControls">
                <button id="fleeButton">${getText('battle_flee')}</button>
            </div>
        `;
        
        // Update Pokemon container content
        console.log('Setting innerHTML for Pokemon-style battle container');
        pokemonContainer.innerHTML = html;
        console.log('Pokemon-style battle UI updated');
        
        // Clear main container except for the Pokemon container
        const children = Array.from(this.element.children);
        children.forEach(child => {
            if (child.id !== 'battleContainer-pokemon') {
                this.element.removeChild(child);
            }
        });
        
        // Add event listeners to action buttons
        const actionButtons = this.element.querySelectorAll('.battle-action');
        console.log('Found action buttons:', actionButtons.length);
        actionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const index = parseInt(button.getAttribute('data-index'));
                this.performPlayerAction(index);
            });
        });
        
        // Add event listener to flee button
        const fleeButton = this.element.querySelector('#fleeButton');
        if (fleeButton) {
            fleeButton.addEventListener('click', () => {
                // 50% chance to successfully flee
                if (Math.random() < 0.5) {
                    this.endBattle('flee');
                } else {
                    this.addLogMessage(getText('battle_flee_fail'));
                    // Enemy gets a free attack if flee fails
                    this.currentTurn = 'enemy';
                    setTimeout(() => this.performEnemyAction(), 500);
                }
            });
        }
    }
};