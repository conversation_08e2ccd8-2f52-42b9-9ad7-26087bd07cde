import React = require("react");
import { InventoryItemStack, Item } from "src/Interfaces";
import { ItemIcon } from "../common";

export const InventoryItem = (props: {
    itemStack: InventoryItemStack,
    itemDef: Item
}) => {
   return (
      <>
        <ItemIcon itemDef={props.itemDef} />
        {props.itemStack.quantity > 1 && <span className="item-quantity">{props.itemStack.quantity}</span>}
      </>
   )
};
