// Language selector component for mapgen2

import { Languages, setLanguage, currentLanguage } from './i18n.js';

// Create and initialize the language selector
export function initLanguageSelector() {
    // Create language selector container
    const container = document.createElement('div');
    container.id = 'language-selector';
    container.style.position = 'fixed';
    container.style.top = '10px';
    container.style.right = '10px';
    container.style.zIndex = '1000';
    container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    container.style.padding = '5px';
    container.style.borderRadius = '5px';
    container.style.color = 'white';
    
    // Create language selector dropdown
    const select = document.createElement('select');
    select.id = 'language-select';
    select.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    select.style.color = 'white';
    select.style.border = '1px solid #666';
    select.style.padding = '3px';
    select.style.borderRadius = '3px';
    
    // Add language options
    for (const [code, name] of Object.entries(Languages)) {
        const option = document.createElement('option');
        option.value = code;
        option.textContent = name;
        option.selected = code === currentLanguage;
        select.appendChild(option);
    }
    
    // Add event listener for language change
    select.addEventListener('change', (e) => {
        setLanguage(e.target.value);
    });
    
    // Add label
    const label = document.createElement('label');
    label.htmlFor = 'language-select';
    label.textContent = 'Language: ';
    
    // Append elements
    container.appendChild(label);
    container.appendChild(select);
    
    // Add to document
    document.body.appendChild(container);
    
    return container;
}