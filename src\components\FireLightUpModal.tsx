import React, { useState, useEffect } from 'react';
import { getText } from '../i18n';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { Item, PlacedBuilding } from '../Interfaces';
import { useRootStore } from '../stores/rootStore';
import { ItemIcon } from './common';
import { startProgressBar } from '../gameinfo';
import { showPromptOverlay } from '../util';
import { Items } from 'src/enums/resources';

interface FireLightUpModalProps {
  building: PlacedBuilding;
  isOpen: boolean;
  onClose: () => void;
}

export const FireLightUpModal: React.FC<FireLightUpModalProps> = ({
  building,
  isOpen,
  onClose
}) => {
  const { itemStacks, removeItemsFromInventory, updateBuildingProperties } = useRootStore();
  const [selectedFlammables, setSelectedFlammables] = useState<{ itemId: string, quantity: number }[]>([]);
  const [totalBurnTime, setTotalBurnTime] = useState(0);

  // Reset selected flammables when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedFlammables([]);
      setTotalBurnTime(0);
    }
  }, [isOpen]);

  // Get all flammable items from inventory
  const flammableItems = itemStacks.filter(stack => {
    if (!stack) return false;
    const item = Items[stack.itemId];
    return item && item['isFlammable'];
  });
  console.log("flammableItems", flammableItems, itemStacks);

  // Calculate total burn time
  useEffect(() => {
    let burnTime = 0;
    selectedFlammables.forEach(item => {
      const itemDef = Items[item.itemId];
      if (itemDef && itemDef.burnTime) {
        burnTime += itemDef.burnTime * item.quantity;
      }
    });
    setTotalBurnTime(burnTime);
  }, [selectedFlammables]);

  // Add flammable item to the fire
  const addFlammable = (itemId: string) => {
    // Find the inventory stack for this item
    const inventoryStack = itemStacks.find(stack => stack && stack.itemId === itemId);
    if (!inventoryStack) return;

    // Calculate how many of this item are already in the fire
    const existingItem = selectedFlammables.find(item => item.itemId === itemId);
    const currentlySelected = existingItem ? existingItem.quantity : 0;

    // Check if we have any more of this item available to add
    if (currentlySelected >= inventoryStack.quantity) {
      // Can't add more than what's in inventory
      showPromptOverlay(getText('No more of this item available'));
      return;
    }

    // Add the item to selected flammables
    if (existingItem) {
      setSelectedFlammables(prev =>
        prev.map(item =>
          item.itemId === itemId
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      );
    } else {
      setSelectedFlammables(prev => [...prev, { itemId, quantity: 1 }]);
    }
  };

  // Remove flammable item from the fire
  const removeFlammable = (itemId: string) => {
    const existingItem = selectedFlammables.find(item => item.itemId === itemId);

    if (existingItem && existingItem.quantity > 1) {
      setSelectedFlammables(prev =>
        prev.map(item =>
          item.itemId === itemId
            ? { ...item, quantity: item.quantity - 1 }
            : item
        )
      );
    } else {
      setSelectedFlammables(prev => prev.filter(item => item.itemId !== itemId));
    }
  };

  // Light the fire
  const lightFire = () => {
    if (selectedFlammables.length === 0) return;

    startProgressBar(getText('Lighting Fire'), 10, () => {
      // Remove flammables from inventory
      const itemsToRemove = selectedFlammables.map(item => ({
        itemDef: Items[item.itemId],
        quantity: item.quantity
      }));

      removeItemsFromInventory(itemsToRemove);

      // Update building state using the store function
      updateBuildingProperties(building, {
        isLit: true,
        fireDuration: totalBurnTime
      });

      // Show success message
      showPromptOverlay(getText('Fire lit!'));
      onClose();
    });
  };

  return (
    <MacOSModal
      title={getText('Light Fire')}
      isOpen={isOpen}
      onClose={onClose}
      initialSize={{ width: 600, height: 500 }}
    >
      <div className="fire-light-up-modal">
        <h3>{getText('Available Flammables')}</h3>
        <div className="inventory-grid">
          {flammableItems.map((stack, index) => {
            // Calculate remaining quantity after selected items
            const selectedItem = selectedFlammables.find(item => item.itemId === stack.itemId);
            const selectedQuantity = selectedItem ? selectedItem.quantity : 0;
            const remainingQuantity = stack.quantity - selectedQuantity;

            return (
              <div
                key={index}
                className={`inventory-item ${remainingQuantity === 0 ? 'depleted' : ''}`}
                onClick={() => addFlammable(stack.itemId)}
              >
                <ItemIcon itemDef={Items[stack.itemId]} />
                <div className="item-quantity">{remainingQuantity}</div>
                {selectedQuantity > 0 && (
                  <div className="item-selected-quantity">({selectedQuantity} {getText('selected')})</div>
                )}
              </div>
            );
          })}
        </div>

        <h3>{getText('Added Flammables')}</h3>
        <div className="inventory-grid">
          {selectedFlammables.map((item, index) => (
            <div
              key={index}
              className="inventory-item"
              onClick={() => removeFlammable(item.itemId)}
            >
              <ItemIcon itemDef={Items[item.itemId]} />
              <div className="item-quantity">{item.quantity}</div>
            </div>
          ))}
        </div>

        <div className="fire-duration">
          <p>{getText('Fire Duration')}: {totalBurnTime} {getText('minutes')}</p>
        </div>

        <button
          className="light-fire-button"
          disabled={selectedFlammables.length === 0}
          onClick={lightFire}
        >
          {getText('Light Fire')}
        </button>
      </div>
    </MacOSModal>
  );
};
