import SimplexNoise from 'simplex-noise';
import { makeRandFloat } from '@redblobgames/prng';

interface ExploredArea {
    x: number;
    y: number;
    radius: number;
    timestamp: number;
}

export class FogOfWar {
    private exploredAreas: ExploredArea[] = [];
    private fogCanvas: HTMLCanvasElement;
    private fogCtx: CanvasRenderingContext2D;
    private noise: any;
    private worldWidth: number;
    private worldHeight: number;
    private visibilityRadius: number = 80; // Base visibility radius
    private noiseScale: number = 0.02; // Scale for noise-based irregular boundaries
    private noiseAmplitude: number = 15; // How much the noise affects the boundary
    
    constructor(worldWidth: number, worldHeight: number) {
        this.worldWidth = worldWidth;
        this.worldHeight = worldHeight;
        
        // Create off-screen canvas for fog rendering
        this.fogCanvas = document.createElement('canvas');
        this.fogCanvas.width = worldWidth;
        this.fogCanvas.height = worldHeight;
        this.fogCtx = this.fogCanvas.getContext('2d')!;
        
        // Initialize noise for irregular boundaries
        this.noise = new (SimplexNoise as any)(makeRandFloat(12345));
        
        // Initialize with full fog
        this.clearFog();
    }
    
    /**
     * Clear the fog canvas and fill with black fog
     */
    private clearFog(): void {
        // this.fogCtx.fillStyle = 'rgba(0, 0, 0, 0.85)';
        this.fogCtx.fillStyle = 'rgba(0, 0, 0, 1)';
        this.fogCtx.fillRect(0, 0, this.worldWidth, this.worldHeight);
    }
    
    /**
     * Update explored areas based on player position
     */
    public updateExploration(playerX: number, playerY: number): void {
        const currentTime = Date.now();

        // Add new explored area
        this.exploredAreas.push({
            x: playerX,
            y: playerY,
            radius: this.visibilityRadius,
            timestamp: currentTime
        });

        // Remove old explored areas to prevent memory buildup (keep last 1000)
        if (this.exploredAreas.length > 1000) {
            this.exploredAreas = this.exploredAreas.slice(-1000);
        }

        console.log(`Fog of War: Explored areas count: ${this.exploredAreas.length}, Player at: (${playerX.toFixed(1)}, ${playerY.toFixed(1)})`);

        this.regenerateFog();
    }
    
    /**
     * Regenerate the fog based on all explored areas
     */
    private regenerateFog(): void {
        // Clear and fill with fog
        this.clearFog();
        
        // Use destination-out to "cut holes" in the fog for explored areas
        this.fogCtx.globalCompositeOperation = 'destination-out';
        
        for (const area of this.exploredAreas) {
            this.drawExploredArea(area);
        }
        
        // Reset composite operation
        this.fogCtx.globalCompositeOperation = 'source-over';
    }
    
    /**
     * Draw an explored area with irregular, blurred boundaries
     */
    private drawExploredArea(area: ExploredArea): void {
        const centerX = area.x;
        const centerY = area.y;
        const baseRadius = area.radius;
        
        // Create a gradient for smooth falloff
        const gradient = this.fogCtx.createRadialGradient(
            centerX, centerY, 0,
            centerX, centerY, baseRadius * 1.2
        );
        gradient.addColorStop(0, 'rgba(255, 255, 255, 1)'); // Fully revealed at center
        gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.8)'); // Mostly revealed
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)'); // Fade to fog at edges
        
        this.fogCtx.fillStyle = gradient;
        
        // Draw irregular circle using noise for boundary variation
        this.fogCtx.beginPath();
        
        const numPoints = 64; // Number of points for the irregular circle
        let firstPoint = true;
        
        for (let i = 0; i <= numPoints; i++) {
            const angle = (i / numPoints) * Math.PI * 2;
            
            // Get noise value for this angle to create irregular boundary
            const noiseX = centerX + Math.cos(angle) * baseRadius * 0.5;
            const noiseY = centerY + Math.sin(angle) * baseRadius * 0.5;
            const noiseValue = this.noise.noise2D(noiseX * this.noiseScale, noiseY * this.noiseScale);
            
            // Apply noise to radius for irregular boundary
            const irregularRadius = baseRadius + (noiseValue * this.noiseAmplitude);
            
            const x = centerX + Math.cos(angle) * irregularRadius;
            const y = centerY + Math.sin(angle) * irregularRadius;
            
            if (firstPoint) {
                this.fogCtx.moveTo(x, y);
                firstPoint = false;
            } else {
                this.fogCtx.lineTo(x, y);
            }
        }
        
        this.fogCtx.closePath();
        this.fogCtx.fill();
    }
    
    /**
     * Render the fog overlay on the main canvas
     */
    public renderFog(ctx: CanvasRenderingContext2D, cameraX: number, cameraY: number): void {
        ctx.save();

        // Set blend mode for fog overlay - using source-over for better visibility
        ctx.globalCompositeOperation = 'source-over';
        ctx.globalAlpha = 1.0;

        // Draw the fog canvas
        ctx.drawImage(this.fogCanvas, -cameraX, -cameraY);

        ctx.restore();
    }
    
    /**
     * Check if a point is explored (for gameplay logic)
     */
    public isExplored(x: number, y: number): boolean {
        for (const area of this.exploredAreas) {
            const dx = x - area.x;
            const dy = y - area.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance <= area.radius) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get the fog canvas for debugging or other purposes
     */
    public getFogCanvas(): HTMLCanvasElement {
        return this.fogCanvas;
    }
    
    /**
     * Update world size if needed
     */
    public updateWorldSize(width: number, height: number): void {
        if (this.worldWidth !== width || this.worldHeight !== height) {
            this.worldWidth = width;
            this.worldHeight = height;
            this.fogCanvas.width = width;
            this.fogCanvas.height = height;
            this.regenerateFog();
        }
    }
    
    /**
     * Set visibility radius
     */
    public setVisibilityRadius(radius: number): void {
        this.visibilityRadius = radius;
    }
    
    /**
     * Clear all exploration (reset fog of war)
     */
    public resetExploration(): void {
        this.exploredAreas = [];
        this.clearFog();
    }
}
